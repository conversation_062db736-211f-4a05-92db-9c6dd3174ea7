import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { useConfigStore } from '@/store';
import TOTP from '@/utils/totp';
import { triggerVibrate, requestCameraPermission } from '@/utils/common';
import { $t } from '@/locale/index.js';

export default function useController() {
	// #ifdef APP-PLUS
	const mpaasScanModule = uni.requireNativePlugin('Mpaas-Scan-Module');
	// #endif

	const pageContainer = ref(null);

	const { accountList } = useConfigStore().getReactiveState();

	const currentCode = ref([]);
	const timeLeft = ref(30);
	let timer = null;
	let canvasTimer = null;

	// 长按操作相关
	const showFeedback = ref(false);
	const feedbackIndex = ref(-1);
	let feedbackTimer = null;

	// 悬浮菜单相关
	const showContextMenu = ref(false);
	const activeIndex = ref(-1);
	const activeAccount = ref(null);
	const menuPosition = ref({
		top: '0px',
		left: '0px',
	});

	const renameRemark = ref('');
	const showRenameDialog = ref(false);

	// 删除账号相关
	const deleteModel = ref(false);

	// 开始扫码
	const startScan = async () => {
		try {
			await requestCameraPermission();
		} catch (error) {
			pageContainer.value.notify.error(error.message);
			return;
		}
		// #ifdef APP-PLUS
		mpaasScanModule.mpaasScan(
			{
				// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
				scanType: ['qrCode', 'barCode'],
				// 是否隐藏相册，默认false不隐藏
				hideAlbum: false,
				//ios需要设置这个参数，只支持中英文 zh-Hans、en，默认中文
				language: 'zh-Hans',
				//相册选择照片识别错误提示(ios)
				failedMsg: $t('checkAuth.unrecognizedQRCode'),
				//Android支持全屏需要设置此参数
				screenType: 'full',
				timeoutInterval: '10', //设置超时时间
				timeoutText: $t('checkAuth.timeoutText'), //超时提醒文本
			},
			(ret) => {
				let codeResult = ret.resp_result;
				if (codeResult.indexOf('otpauth://totp/') !== -1) {
					uni.showLoading({
						title: '请稍候...',
						mask: true,
					});
					// 动态口令码
					setCode(codeResult);
				} else {
					uni.hideLoading();
					uni.showToast($t('checkAuth.unrecognizedQRCode'), 2000);
				}
			},
		);
		// #endif
		// #ifdef APP-HARMONY
		uni.scanCode({
			onlyFromCamera: true,
			scanType: ['qrCode'],
			success(res) {
				uni.showLoading({
					title: '请稍候...',
					mask: true,
				});

				let codeResult = res.result;

				if (codeResult.indexOf('otpauth://totp/') !== -1) {
					// 动态口令码
					setCode(codeResult);
				} else {
					uni.hideLoading();
					uni.showToast($t('checkAuth.unrecognizedQRCode'), 2000);
				}
			},
		});
		// #endif
	};

	// 设置验证码
	const setCode = (codeResult) => {
		let account = codeResult.substring(codeResult.lastIndexOf(':') + 1, codeResult.indexOf('?')),
			secret = codeResult.substring(codeResult.indexOf('secret=') + 7, codeResult.indexOf('&issuer')),
			name = $t('server.btPanel'),
			issuer = '';
		if (codeResult.indexOf('--') !== -1) {
			name = decodeURI(codeResult.substring(codeResult.indexOf('issuer=') + 7, codeResult.lastIndexOf('--')));
			issuer = codeResult.substring(codeResult.lastIndexOf('--') + 2, codeResult.length);
		} else {
			issuer = codeResult.substring(codeResult.indexOf('issuer=') + 7, codeResult.length);
		}
		let item = {
			name: name,
			account: account,
			secret: secret,
			issuer: issuer,
		};
		accountList.value.push(item);
		accountList.value = Array.from(
			accountList.value.reduce((map, item) => map.set(item.secret, item), new Map()).values(),
		);
		uni.setStorageSync('accountList', accountList.value);
		setTimeout(() => {
			handleAddSuccess();
		}, 1000);
	};

	const handleAddSuccess = () => {
		uni.hideLoading();
		pageContainer.value.notify.success($t('checkAuth.addSuccess'));
	};

	// 显示悬浮菜单
	const showFloatingMenu = (account, index, event) => {
		// 执行振动
		triggerVibrate();

		activeIndex.value = index;
		activeAccount.value = account;

		// 获取被长按元素相对于页面的位置
		uni.createSelectorQuery()
			.selectAll('.auth-item')
			.boundingClientRect((rects) => {
				if (rects && rects[index]) {
					const rect = rects[index];

					// 获取系统信息，用于检测是否会超出屏幕
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;

					// 获取tabbar高度（自定义tabbar一般是100rpx, 即大约50px）
					const tabbarHeight = 60;

					// 计算有效可用屏幕高度 = 屏幕高度 - tabbar高度
					const availableHeight = screenHeight - tabbarHeight;

					// 预估菜单高度
					const estimatedMenuHeight = 160; // 根据3个菜单项+分隔线估算

					// 计算菜单位置
					let menuTop, menuOffsetY;
					let menuClass = '';
					const menuLeft = rect.left + rect.width / 2; // 水平居中于卡片

					// 预先判断菜单显示位置（顶部还是底部）
					// 使用预估高度先判断，避免出现位置闪跳
					const shouldShowAtTop = rect.top + rect.height + estimatedMenuHeight > availableHeight;

					if (shouldShowAtTop) {
						// 如果会超出屏幕底部，将菜单显示在卡片上方
						menuOffsetY = -estimatedMenuHeight; // 减小上方间距到5px
						menuTop = rect.top + menuOffsetY;
						menuClass = 'menu-top';
					} else {
						// 否则显示在卡片下方
						menuOffsetY = rect.height; // 减小下方间距到5px
						menuTop = rect.top + menuOffsetY;
						menuClass = 'menu-bottom';
					}

					// 先设置菜单位置和样式
					menuPosition.value = {
						top: `${menuTop}px`,
						left: `${menuLeft}px`,
						class: menuClass,
					};

					// 显示菜单（在设置位置之后）
					showContextMenu.value = true;

					// 锁定页面滚动
					const pageScrollFunc = (e) => {
						// 阻止页面默认滚动行为
						e.preventDefault();
						e.stopPropagation();
						return false;
					};

					// 添加事件监听
					setTimeout(() => {
						uni.$on('touchmove', pageScrollFunc, { capture: true });
					}, 10);

					// 在hideContextMenu中解除事件监听
					const originalHideContextMenu = hideContextMenu;
					hideContextMenu = () => {
						// 解除事件监听
						uni.$off('touchmove', pageScrollFunc);
						// 调用原始方法
						originalHideContextMenu();
					};

					// 菜单出现后，查询其实际高度并微调位置
					nextTick(() => {
						// 激活克隆项canvas
						if (showContextMenu.value && activeIndex.value === index) {
							const cloneCanvasId = 'countdown-clone-' + index;
							const ctx = uni.createCanvasContext(cloneCanvasId);
							const percentage = timeLeft.value / 30;
							drawCircle(ctx, percentage);
							ctx.draw();
						}

						// 查询菜单的实际尺寸进行微调
						uni.createSelectorQuery()
							.select('.context-menu')
							.boundingClientRect((menuRect) => {
								if (!menuRect) return;

								// 仅微调位置，但不改变显示方向（顶部或底部）
								if (shouldShowAtTop) {
									// 上方显示的微调
									menuTop = rect.top - menuRect.height; // 减小上方间距到5px

									// 如果顶部空间不足，则适当下移（但不切换到底部）
									if (menuTop < 10) {
										menuTop = 10; // 至少保留10px顶部间距
									}
								} else {
									// 下方显示的微调
									// 无需特殊处理，已在初始计算中考虑
								}

								// 更新菜单位置（仅调整top值，不改变class）
								menuPosition.value = {
									top: `${menuTop}px`,
									left: `${menuLeft}px`,
									class: menuClass,
								};
							})
							.exec();
					});
				}
			})
			.exec();
	};

	// 隐藏悬浮菜单
	const hideContextMenu = () => {
		showContextMenu.value = false;
		activeIndex.value = -1;
		activeAccount.value = null;
	};

	// 复制验证码
	const copyCode = () => {
		if (activeIndex.value >= 0) {
			uni.setClipboardData({
				data: currentCode.value[activeIndex.value] || '',
				showToast: false,
				success: () => {
					pageContainer.value.notify.success($t('checkAuth.copySuccess'));
					showFeedbackMessage(activeIndex.value);
					hideContextMenu();
				},
			});
		}
	};

	// 重命名账号
	const confirmRename = (close) => {
		if (renameRemark.value == '') return;
		if (activeIndex.value >= 0 && activeAccount.value) {
			activeAccount.value.name = renameRemark.value;
			close && close();
			accountList.value[activeIndex.value].name = renameRemark.value;
			uni.setStorageSync('accountList', accountList.value);
			hideContextMenu();
			pageContainer.value.notify.success($t('checkAuth.modifySuccess'));
		}
	};

	// 删除账号
	const deleteAccount = (close) => {
		if (activeIndex.value >= 0) {
			accountList.value.splice(activeIndex.value, 1);
			// 实际应用中应该保存到存储
			uni.setStorageSync('accountList', accountList.value);
			showFeedbackMessage(-1);

			close && close();

			hideContextMenu();
			pageContainer.value.notify.success($t('checkAuth.deleteSuccess'));
		}
	};

	// 显示操作反馈
	const showFeedbackMessage = (index) => {
		feedbackIndex.value = index;
		showFeedback.value = true;

		if (feedbackTimer) clearTimeout(feedbackTimer);

		feedbackTimer = setTimeout(() => {
			showFeedback.value = false;
			feedbackIndex.value = -1;
		}, 1500);
	};

	// 生成真实TOTP验证码
	const generateTOTP = () => {
		if (accountList.value.length === 0) return;

		accountList.value.forEach((account, index) => {
			try {
				// 使用真实的TOTP算法生成验证码
				const totpInstance = new TOTP(account.secret, 30);
				const code = totpInstance.getToken();
				currentCode.value[index] = code;
			} catch (error) {
				console.error('生成验证码错误:', error);
				currentCode.value[index] = '------';
			}
		});
	};

	// 倒计时相关
	const startCountdown = () => {
		// 使用accountList中的secret获取剩余秒数
		if (accountList.value.length === 0) return;

		// 使用第一个有效账号的secret
		const firstValidAccount = accountList.value.find((acc) => acc.secret && acc.secret.length >= 16) || {
			secret: 'ABCDEFGHIJKLGHIJ',
		}; // 兜底密钥

		const totpInstance = new TOTP(firstValidAccount.secret, 30);
		timeLeft.value = Math.floor(totpInstance.getRemainingSeconds());

		// 更新环形进度
		updateCanvasProgress();

		// 清除可能存在的定时器
		if (timer) clearInterval(timer);

		// 将时间同步到整秒, 再开始轮询
		const sync2NextSecond = () => {
			const ms2NextSecond = 1000 - (Date.now() % 1000);
			setTimeout(startTimer, ms2NextSecond);
		};

		const startTimer = () => {
			// 在整秒处重新获取剩余时间和生成验证码
			const refreshTotp = new TOTP(firstValidAccount.secret, 30);
			timeLeft.value = Math.floor(refreshTotp.getRemainingSeconds());
			generateTOTP();

			timer = setInterval(() => {
				timeLeft.value -= 1;

				if (timeLeft.value <= 0) {
					// 重新获取剩余秒数，确保同步
					const refreshTotp = new TOTP(firstValidAccount.secret, 30);
					timeLeft.value = Math.floor(refreshTotp.getRemainingSeconds());
					generateTOTP();
				}
			}, 1000);
		};

		// 开始同步过程
		sync2NextSecond();
	};

	// 绘制Canvas倒计时环
	const updateCanvasProgress = () => {
		if (canvasTimer) clearInterval(canvasTimer);

		canvasTimer = setInterval(() => {
			accountList.value.forEach((_, index) => {
				const canvasId = 'countdown-' + index;
				const ctx = uni.createCanvasContext(canvasId);
				const percentage = timeLeft.value / 30;
				drawCircle(ctx, percentage);
				ctx.draw();

				// 如果克隆项存在，同时更新克隆项的Canvas
				if (showContextMenu.value && activeIndex.value === index) {
					const cloneCanvasId = 'countdown-clone-' + index;
					const cloneCtx = uni.createCanvasContext(cloneCanvasId);
					drawCircle(cloneCtx, percentage);
					cloneCtx.draw();
				}
			});
		}, 100);
	};

	// 绘制环形进度条
	const drawCircle = (ctx, percentage) => {
		const radius = 15;
		const centerX = 16;
		const centerY = 16;

		// 绘制底环
		ctx.beginPath();
		ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
		ctx.setStrokeStyle('#e0e0e0');
		ctx.setLineWidth(2);
		ctx.stroke();

		// 绘制进度环
		ctx.beginPath();
		// 从12点钟方向开始，逆时针绘制
		const startAngle = -0.5 * Math.PI;
		const endAngle = startAngle + 2 * Math.PI * percentage;
		ctx.arc(centerX, centerY, radius, startAngle, endAngle);

		// 根据剩余时间变化颜色
		let color = '#20a50a';
		if (timeLeft.value <= 10) {
			// 在最后10秒内，颜色逐渐变成红色
			const redPercentage = (10 - timeLeft.value) / 10;
			color =
				timeLeft.value <= 5
					? '#ff3b30'
					: `rgb(${Math.floor(32 + 223 * redPercentage)}, ${Math.floor(
							165 - 123 * redPercentage,
					  )}, ${Math.floor(10 - 2 * redPercentage)})`;
		}

		ctx.setStrokeStyle(color);
		ctx.setLineWidth(2);
		ctx.stroke();
	};

	onMounted(() => {
		// 不再直接初始化，改为通过watch监听
	});

	// 监听accountList变化
	watch(
		() => accountList.value.length,
		(newLength) => {
			if (newLength > 0) {
				// 当accountList有数据时才开始生成TOTP和倒计时
				generateTOTP();
				startCountdown();
			}
		},
		{ immediate: true }, // 立即检查当前值
	);

	onBeforeUnmount(() => {
		if (timer) clearInterval(timer);
		if (canvasTimer) clearInterval(canvasTimer);
		if (feedbackTimer) clearTimeout(feedbackTimer);
	});

	return {
		accountList,
		currentCode,
		timeLeft,
		showFeedback,
		feedbackIndex,
		showContextMenu,
		activeIndex,
		activeAccount,
		menuPosition,
		showFloatingMenu,
		hideContextMenu,
		copyCode,
		deleteAccount,
		startScan,
		deleteModel,
		renameRemark,
		confirmRename,
		showRenameDialog,
		pageContainer,
	};
}
