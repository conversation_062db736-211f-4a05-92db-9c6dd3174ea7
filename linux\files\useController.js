import { ref, computed, nextTick } from 'vue';
import { getFileList, deleteFile, setFileName, setFilePs, createNewFile } from '@/api/files';
import { formatTime } from '@/utils';
import { isUndefined } from '@/utils/type';
import { triggerVibrate } from '@/utils/common';
import { $t } from '@/locale/index.js';

const DEFAULT_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';
export const currentPath = ref('/');
export const currentDisk = ref('/');
export const dirList = ref([]);
export const fileList = ref([]);
export const paging = ref(null);
export const pageContainer = ref(null);
export const showDeleteDialog = ref(false); // 删除确认弹窗
export const showRenameDialog = ref(false); // 重命名弹窗
export const renameFileName = ref(''); // 重命名文件名
export const showRemarkDialog = ref(false); // 修改备注弹窗
export const renameRemark = ref(''); // 修改备注
export const showCreateDialog = ref(false); // 新建文件夹或文件弹窗
export const createFileType = ref('folder'); // 新建文件类型
export const createFileName = ref(''); // 新建文件名

export const actionList = [
	{
		name: $t('files.newFolder'),
		fontSize: '32rpx',
		color: 'var(--text-color-primary)',
		index: 0,
	},
	{
		name: $t('files.newFile'),
		fontSize: '32rpx',
		color: 'var(--text-color-primary)',
		index: 1,
	},
];
export const actionSheet = ref(null);

// 导航栏下拉菜单状态
export const showNavMenu = ref(false);

// 切换导航栏下拉菜单
export const toggleNavMenu = () => {
	showNavMenu.value = !showNavMenu.value;
	// 关闭上下文菜单
	if (showContextMenu.value) {
		hideContextMenu();
	}
};

// 隐藏导航菜单
export const hideNavMenu = () => {
	showNavMenu.value = false;
};

// 刷新文件列表
export const handleRefresh = () => {
	hideNavMenu();
	if (paging.value) {
		paging.value.reload();
	}
};

const getFile = (val, type) => {
	const fileArr = val.split(';');
	const fileName = fileArr[0];
	const ext = getExtIcon(fileName, type);
	const time = formatTime(fileArr[2]);
	const permission = fileArr[3];
	const owner = fileArr[4];
	const path = getPath(currentPath.value, fileName);
	const fileMsg = getBtPs(fileName, path);
	return {
		path,
		fileName,
		time,
		permission,
		owner,
		isFile: type !== 'dir',
		type,
		isLink: fileArr[5] || '',
		ext,
		icon: determineFileType(ext), // 文件图标
		size: fileArr[1] || 0,
		ps: fileMsg || fileArr[10] || '',
	};
};

export const getFilesList = async (p = 1, showRow = 100) => {
	dirList.value = [];
	fileList.value = [];
	try {
		const res = await getFileList({
			path: currentPath.value,
			p,
			showRow,
		});
		if (DEFAULT_TYPE === 'domestic') {
			handleDomesticFilesList(res);
		} else {
			handleInternationalFilesList(res);
		}
		return [...dirList.value, ...fileList.value];
	} catch (error) {
		console.error(error);
	}
};

const handleDomesticFilesList = async (res) => {
	const { path, dir, files, store, bt_sync, page, tamper_data, search_history, file_recycle } = res;
	let { dirs: tDirs, files: tFiles, msg: pMsg, tip, rules } = tamper_data;
	const isTamperOpen = tip === 'ok'; // 插件防篡改是否开启状态
	const imageNameList = [];
	// 循环目录
	dir.forEach((item, index) => {
		dirList.value.push(reconstructionFile('dir', item, pMsg ? '' : tDirs[index], path, bt_sync));
	});

	// 循环文件
	fileList.value = [];
	files.forEach((item, index) => {
		const file = reconstructionFile('file', item, pMsg ? '' : tFiles[index], path);
		if (file.icon === 'images') {
			imageNameList.push(file.fileName);
		}
		fileList.value.push(file);
	});
};

const handleInternationalFilesList = async (res) => {
	const { PATH: path, DIR: dir, FILES: files } = res;
	dir.forEach((item, index) => {
		dirList.value.push(getFile(item, 'dir'));
	});
	files.forEach((item, index) => {
		fileList.value.push(getFile(item, 'file'));
	});
};

const reconstructionFile = (type, FileItem, TamperData = '0;0', path, bt_sync = []) => {
	const ext = getExtIcon(FileItem.nm, type);
	const fileMsg = getBtPs(FileItem.nm, path);
	const filePath = getPath(path, FileItem.nm);
	const isLock = TamperData.split(';')[0] === '1';
	const tid = TamperData.split(';')[1];
	return {
		icon: determineFileType(ext), // 文件图标
		ext, // 文件后缀
		fileName: FileItem.nm, // 文件名称
		time: formatTime(FileItem.mt), // 时间
		ps: fileMsg || FileItem.rmk || '', // 备注
		size: FileItem.sz, // 文件大小
		type: type, // 文件类型
		isLink: FileItem.lnk, // 软连接
		isShare: Number(FileItem.durl) != 0 ? FileItem.durl : 0, // 是否分享 0否 其它值为分享ID
		isTop: !!FileItem.top, //是否置顶
		isFav: !!Number(FileItem.fav), // 是否收藏
		isNew: false, // 是否新建
		isSync: isSyncType(bt_sync, filePath), // 是否同步
		path: filePath, // 文件路径
		tamperProofId: Number(tid), // 防篡改ID:在防篡改列表中查询
		isFile: type !== 'dir', // 是否是文件
	};
};

const getPath = (path, filename) => {
	return `${path}/${filename}`.replace('//', '/');
};

// 是否同步类型
export const isSyncType = (arr, path) => {
	if (isUndefined(arr) || arr.length == 0) return '';
	const sync = arr.find((item) => item.path === path);
	return sync ? sync.type : '';
};

export const defaultPS = new Map([
	['/etc', 'PS: 系统主要配置文件目录'],
	['/home', 'PS: 用户主目录'],
	['/tmp', 'PS: 公共的临时文件存储点'],
	['/root', 'PS: 系统管理员的主目录'],
	['/home', 'PS: 用户主目录'],
	['/usr', 'PS: 系统应用程序目录'],
	['/boot', 'PS: 系统启动核心目录'],
	['/lib', 'PS: 系统资源文件类库目录'],
	['/mnt', 'PS: 存放临时的映射文件系统'],
	['/www', 'PS: 宝塔面板程序目录'],
	['/bin', 'PS: 存放二进制可执行文件目录'],
	['/dev', 'PS: 存放设备文件目录'],
	['/www/wwwlogs', 'PS: 默认网站日志目录'],
	['/www/server', 'PS: 宝塔软件安装目录'],
	['/www/wwwlogs', 'PS: 网站日志目录'],
	['/www/Recycle_bin', 'PS: 回收站目录,勿动'],
	['/www/server/panel', 'PS: 宝塔主程序目录，勿动'],
	['/www/server/panel/plugin', 'PS: 宝塔插件安装目录'],
	['/www/server/panel/BTPanel', 'PS: 宝塔面板前端文件'],
	['/www/server/panel/BTPanel/static', 'PS: 宝塔面板前端静态文件'],
	['/www/server/panel/BTPanel/templates', 'PS: 宝塔面板前端模板文件'],
]);

export const fileMainType = {
	images: ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'tiff', 'ico', 'JPG', 'webp'],
	compress: ['zip', 'rar', 'gz', 'war', 'tgz', '7z', 'tar.gz', 'tar'],
	video: [
		'mp4',
		'mp3',
		'mpeg',
		'mpg',
		'mov',
		'avi',
		'webm',
		'mkv',
		'mkv',
		'mp3',
		'rmvb',
		'wma',
		'wmv',
		'flac',
		'mov',
	],
	ont_text: [
		'iso',
		'xlsx',
		'xls',
		'doc',
		'docx',
		'tiff',
		'exe',
		'so',
		'bz',
		'dmg',
		'apk',
		'pptx',
		'ppt',
		'xlsb',
		'pdf',
	],
};

// 根据文件类型获取图标类型
export const getFileIconType = (fileType, iconType) => {
	let type = iconType === 'text' || iconType === 'ont_text' ? fileType : iconType;
	switch (type) {
		case 'folder':
			return 'folder-filled';
		case 'images':
			return 'image';
		case 'compress':
			return 'compress';
		case 'video':
			return 'video';
		default:
			return 'file';
	}
};

// 根据文件类型获取图标颜色
export const getFileIconColor = (fileType, iconType) => {
	let type = iconType === 'text' || iconType === 'ont_text' ? fileType : iconType;
	switch (type) {
		case 'folder':
			return '#E6A23C';
		case 'images':
			return '#67C23A';
		case 'compress':
			return '#F56C6C';
		case 'video':
			return '#909399';
		default:
			return '#909399';
	}
};

export const exts = [
	'folder',
	'folder-unempty',
	'sql',
	'c',
	'cpp',
	'cs',
	'flv',
	'css',
	'js',
	'htm',
	'html',
	'java',
	'log',
	'mht',
	'php',
	'url',
	'xml',
	'ai',
	'bmp',
	'cdr',
	'gif',
	'ico',
	'jpeg',
	'jpg',
	'JPG',
	'png',
	'psd',
	'webp',
	'ape',
	'avi',
	'mkv',
	'mov',
	'mp3',
	'mp4',
	'mpeg',
	'mpg',
	'rm',
	'rmvb',
	'swf',
	'wav',
	'webm',
	'wma',
	'wmv',
	'rtf',
	'docx',
	'fdf',
	'potm',
	'pptx',
	'txt',
	'xlsb',
	'xlsx',
	'7z',
	'cab',
	'iso',
	'rar',
	'zip',
	'gz',
	'war',
	'bt',
	'tgz',
	'file',
	'apk',
	'bookfolder',
	'folder-empty',
	'fromchromefolder',
	'documentfolder',
	'fromphonefolder',
	'mix',
	'musicfolder',
	'picturefolder',
	'videofolder',
	'sefolder',
	'access',
	'mdb',
	'accdb',
	'fla',
	'doc',
	'docm',
	'dotx',
	'dotm',
	'dot',
	'pdf',
	'ppt',
	'pptm',
	'pot',
	'xls',
	'csv',
	'xlsm',
	'py',
	'sh',
	'json',
	'lua',
	'bt_split',
	'bt_split_json',
];

const getBtPs = (fileName, path) => {
	let fMsg = '',
		dMsg = defaultPS.get(`${path === '/' ? '/' : `${path}/`}${fileName}`);
	switch (fileName) {
		case '.htaccess':
			fMsg = 'PS: Apache用户配置文件(伪静态)';
			break;
		case 'swap':
			fMsg = 'PS: 宝塔默认设置的SWAP交换分区文件';
			break;
	}
	if (fileName.includes('.upload.tmp')) {
		fMsg = 'PS: 宝塔文件上传临时文件,重新上传从断点续传,可删除';
	}
	if (fileName.includes('.user.ini')) {
		fMsg = 'PS: PHP用户配置文件(防跨站)!';
	}

	if (dMsg) fMsg = dMsg;
	return dMsg || fMsg;
};

/**
 * @description 获取文件类型
 * @param ext 文件后缀
 */
export const determineFileType = (ext) => {
	let returnVal = 'text';
	if (ext) ext = ext.toLowerCase();
	Object.entries(fileMainType).forEach(([key, item]) => {
		item.forEach((items) => {
			if (items == ext) {
				returnVal = key;
			}
		});
	});
	return returnVal;
};

// 获取文件图标
export const getExtIcon = (fileName, type) => {
	if (type === 'dir') return 'folder';
	const extArr = fileName.split('.');
	const extLastName = extArr[extArr.length - 1];
	for (let i = 0; i < exts.length; i++) {
		if (exts[i] == extLastName) {
			return exts[i];
		}
	}
	return 'file';
};

/**
 * 将路径字符串解析为带名称和路径的对象数组
 * @param {string} path - 需要解析的路径，例如'/www/wwwroot'
 * @returns {Array<{name: string, path: string, show: boolean, showFold: boolean, loading: boolean}>} - 解析后的对象数组
 */
export const parsePath = (path) => {
	if (!path) return [{ name: $t('files.rootDirectory'), path: '/', show: true, showFold: false, loading: false }];

	if (path === '/')
		return [{ name: $t('files.rootDirectory'), path: '/', show: true, showFold: false, loading: false }];

	const pathSegments = path.split('/').filter(Boolean);
	const result = [{ name: $t('files.rootDirectory'), path: '/', show: true, showFold: false, loading: false }];

	// 构建累积路径并生成对象数组
	let currentPath = '';
	for (let i = 0; i < pathSegments.length; i++) {
		currentPath += '/' + pathSegments[i];
		result.push({
			name: pathSegments[i],
			path: currentPath,
			show: true,
			showFold: false,
			loading: false,
		});
	}
	return result;
};

// 将 pathList 改为计算属性，自动根据 currentPath 计算
export const pathList = computed(() => parsePath(currentPath.value));

export const cutDirPath = async (path) => {
	if (path === '/.Recycle_bin' || path === '/www/.Recycle_bin') {
		pageContainer.value.notify.error($t('files.recycleError'));
		return false;
	}
	if (path === '') currentPath.value = '/';
	if (currentPath.value === path) return;
	currentPath.value = path;
	paging.value.reload();
};

/**
 * @description 打开文件
 * @param fileItem 文件信息
 */
export const openFile = (fileItem) => {
	if (fileItem.type === 'dir') {
		cutDirPath(fileItem.path);
	} else {
		// 文件类型判断
		switch (determineFileType(fileItem.ext)) {
			case 'images':
				pageContainer.value.notify.error($t('files.imageNotSupported'));
				break;
			case 'compress':
				pageContainer.value.notify.error($t('files.compressNotSupported'));
				break;
			case 'video':
				pageContainer.value.notify.error($t('files.mediaNotSupported'));
				break;
			default:
				openAceEditor(fileItem);
				break;
		}
	}
};

/**
 *@description   根据路径打开文件编辑器
 * @param {string} path 完整文件路径
 * @param {AnyFunction} callback 回调函数
 */
export const openAceEditor = async (fileItems) => {
	const fileItem = {
		path: fileItems.path,
		title: fileItems.path.split('/').pop(),
		size: fileItems.size,
	};

	if (fileItem?.path?.includes('/dev/pts')) {
		pageContainer.value.notify.error($t('files.systemFileError'));
		return;
	}
	if (determineFileType(fileItem.fileName?.split('.').pop()) !== 'text') {
		pageContainer.value.notify.error($t('files.fileTypeNotSupported'));
		return;
	}
	// 判断文件大小
	if (fileItem.size > 3 * 1024 * 1024) {
		pageContainer.value.notify.warning($t('files.fileSizeTooLarge'));
		return;
	}
	FilesAceEditor(fileItem);
};

export const FilesAceEditor = (fileItem) => {
	uni.navigateTo({
		url: `/linux/files/editor/index?fileItem=${JSON.stringify(fileItem)}`,
		animationType: 'zoom-fade-out',
	});
};

// 长按菜单相关
export const showContextMenu = ref(false);
export const activeFile = ref(null);
export const activeIndex = ref(-1);
export const menuPosition = ref({
	top: '0px',
	left: '0px',
	class: '',
});
export const clonePosition = ref({
	top: '0px',
	left: '0px',
	width: '0px',
	height: '0px',
});

// 触摸状态管理
export const touchStartTime = ref(0);
export const touchStartPos = ref({ x: 0, y: 0 });
export const isTouchMoved = ref(false);
export const longPressTimer = ref(null);
export const LONG_PRESS_THRESHOLD = 600; // 长按阈值，单位毫秒
export const MOVE_THRESHOLD = 10; // 移动阈值，单位像素

// 菜单高度管理
export const actualMenuHeight = ref(140);
export const showTempMenu = ref(false);

// 测量菜单高度的方法
export const measureMenuHeight = () => {
	// 显示临时测量菜单
	showTempMenu.value = true;

	// 等待临时菜单渲染完成
	nextTick(() => {
		uni.createSelectorQuery()
			.select('.temp-measure-menu')
			.boundingClientRect((rect) => {
				if (rect && rect.height > 0) {
					actualMenuHeight.value = rect.height;
				}
				// 隐藏临时菜单
				showTempMenu.value = false;
			})
			.exec();
	});
};

// 触摸处理相关函数
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
	}

	// 记录触摸开始时间和位置
	touchStartTime.value = Date.now();
	touchStartPos.value = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved.value = false;

	// 设置长按定时器
	longPressTimer.value = setTimeout(() => {
		if (!isTouchMoved.value) {
			const index = event.currentTarget.dataset.index;
			const fileData = JSON.parse(event.currentTarget.dataset.file);
			showFloatingMenu(fileData, event, index);
		}
	}, LONG_PRESS_THRESHOLD);
};

export const handleTouchMove = (event) => {
	if (!touchStartPos.value) return;

	// 计算移动距离
	const moveX = Math.abs(event.touches[0].clientX - touchStartPos.value.x);
	const moveY = Math.abs(event.touches[0].clientY - touchStartPos.value.y);

	// 如果移动超过阈值，标记为已移动并取消长按定时器
	if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
		isTouchMoved.value = true;

		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
	}
};

export const handleTouchEnd = (event) => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}

	// 如果未移动且是短触摸（非长按），则打开文件
	if (!isTouchMoved.value && Date.now() - touchStartTime.value < LONG_PRESS_THRESHOLD) {
		const fileData = JSON.parse(event.currentTarget.dataset.file);
		openFile(fileData);
	}
};

export const handleTouchCancel = () => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

// 显示悬浮菜单 - 两阶段定位
export const showFloatingMenu = (file, event, index) => {
	// 触感反馈
	triggerVibrate();

	activeFile.value = file;
	activeIndex.value = index;

	// 使用传入的事件位置获取精确的触摸点
	const touchX = event.touches[0].clientX;
	const touchY = event.touches[0].clientY;

	// 获取系统信息，用于检测是否会超出屏幕
	const systemInfo = uni.getSystemInfoSync();
	const screenHeight = systemInfo.windowHeight;
	const screenWidth = systemInfo.windowWidth;

	// 获取被长按元素相对于页面的位置
	uni.createSelectorQuery()
		.selectAll('.file-item-container')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];

			// 设置克隆项位置
			clonePosition.value = {
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
			};

			// 预估参数
			const tabbarHeight = 60; // 底部导航栏高度
			const headerHeight = 50; // 顶部标题栏高度
			const menuWidth = 150; // 菜单宽度
			const edgeBuffer = 5; // 边缘安全距离 - 减小以使菜单更贴近克隆项

			// 计算菜单位置
			let menuTop,
				menuLeft,
				menuClass = '';

			// 水平定位 - 居中显示，但保持在屏幕内
			menuLeft = rect.left + rect.width / 2;
			// 防止菜单超出屏幕左侧
			if (menuLeft - menuWidth / 2 < edgeBuffer) {
				menuLeft = menuWidth / 2 + edgeBuffer;
			}
			// 防止菜单超出屏幕右侧
			if (menuLeft + menuWidth / 2 > screenWidth - edgeBuffer) {
				menuLeft = screenWidth - menuWidth / 2 - edgeBuffer;
			}

			// 垂直定位 - 智能判断上方还是下方
			// 计算下方可用空间和上方可用空间
			const spaceBelow = screenHeight - rect.bottom - tabbarHeight;
			const spaceAbove = rect.top - headerHeight;

			// 优先考虑下方显示，如果下方空间不足，再考虑上方
			if (spaceBelow >= actualMenuHeight.value + edgeBuffer) {
				// 下方有足够空间
				menuTop = rect.bottom + edgeBuffer;
				menuClass = 'menu-bottom';
			} else if (spaceAbove >= actualMenuHeight.value + edgeBuffer) {
				// 上方有足够空间 - 菜单底部紧贴克隆项顶部
				menuTop = rect.top - actualMenuHeight.value;
				menuClass = 'menu-top menu-position-bottom'; // 添加菜单位置标记
			} else {
				// 两边都没有理想空间，选择空间较大的一边
				if (spaceBelow >= spaceAbove) {
					// 使用下方剩余空间
					menuTop = rect.bottom + edgeBuffer;
					menuClass = 'menu-bottom';
				} else {
					// 使用上方剩余空间 - 菜单底部紧贴克隆项顶部
					menuTop = rect.top - actualMenuHeight.value;
					menuClass = 'menu-top menu-position-bottom';
				}
			}

			// 设置菜单初始位置和样式
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
			};

			// 显示菜单
			showContextMenu.value = true;

			// 第二阶段：在菜单渲染后微调位置
			nextTick(() => {
				// 获取实际菜单高度
				uni.createSelectorQuery()
					.select('.context-menu')
					.boundingClientRect((menuRect) => {
						if (!menuRect) return;

						const actualMenuHeight = menuRect.height;

						// 如果菜单显示在上方，需要向上偏移菜单高度
						if (menuClass.includes('menu-position-bottom')) {
							// 确保菜单底部与克隆项顶部对齐
							const adjustedTop = rect.top - actualMenuHeight;
							menuPosition.value.top = `${adjustedTop}px`;
						}

						// 如果实际菜单宽度与预估不同，调整水平居中
						const actualMenuWidth = menuRect.width;
						if (Math.abs(actualMenuWidth - menuWidth) > 10) {
							// 重新计算水平位置
							let adjustedLeft = rect.left + rect.width / 2;
							if (adjustedLeft - actualMenuWidth / 2 < edgeBuffer) {
								adjustedLeft = actualMenuWidth / 2 + edgeBuffer;
							}
							if (adjustedLeft + actualMenuWidth / 2 > screenWidth - edgeBuffer) {
								adjustedLeft = screenWidth - actualMenuWidth / 2 - edgeBuffer;
							}
							menuPosition.value.left = `${adjustedLeft}px`;
						}
					})
					.exec();
			});
		})
		.exec();
};

// 隐藏悬浮菜单
export const hideContextMenu = () => {
	showContextMenu.value = false;
	activeFile.value = null;
	activeIndex.value = -1;
};

// 文件操作相关方法
// 修改备注
export const confirmRemark = async (close) => {
	try {
		if (activeFile.value) {
			const res = await setFilePs({
				filename: activeFile.value.path,
				ps_type: 0,
				ps_body: renameRemark.value,
			});
			if (res.status) {
				paging.value.reload();
				pageContainer.value.notify.success(res.msg);
			} else {
				pageContainer.value.notify.error(res.msg);
			}
			renameRemark.value = '';
			close && close();
			hideContextMenu();
		}
	} catch (error) {
		console.error(error);
	} finally {
		uni.hideLoading();
	}
};

const getChangePath = (path, fileName) => {
	const pathRegex = /^(.*\/)/;
	const match = path.match(pathRegex);
	const fullPath = match ? match[1] : '';
	return `${fullPath}${fileName}`;
};

/**
 * @description 匹配非法字符
 * @param {string} path 配置对象
 * @return 返回匹配结果
 */
export const matchUnqualifiedString = (path) => {
	var containSpecial = RegExp(/[\*\|\:\\\"\/\<\>\?]+/);
	return containSpecial.test(path);
};

// 重命名文件
export const renameFile = async (close) => {
	try {
		if (activeFile.value) {
			if (matchUnqualifiedString(renameFileName.value)) {
				pageContainer.value.notify.error($t('files.invalidChars'));
				return;
			}
			if (renameFileName.value === '') {
				pageContainer.value.notify.error($t('files.nameEmpty'));
				return;
			}
			if (renameFileName.value === activeFile.value.fileName) {
				pageContainer.value.notify.error($t('files.nameNoChange'));
				return;
			}
			uni.showLoading({
				title: $t('files.renaming'),
			});
			const res = await setFileName({
				sfile: getChangePath(activeFile.value.path, activeFile.value.fileName),
				dfile: getChangePath(activeFile.value.path, renameFileName.value),
				rename: true,
			});
			if (res.status) {
				paging.value.reload();
				pageContainer.value.notify.success(res.msg);
			} else {
				pageContainer.value.notify.error(res.msg);
			}
			renameFileName.value = '';
			close && close();
			hideContextMenu();
		}
	} catch (error) {
		console.error(error);
	} finally {
		uni.hideLoading();
	}
};

// 确认删除文件
export const confirmDeleteFile = async (close) => {
	if (activeFile.value) {
		uni.showLoading({
			title: $t('files.deleting'),
		});
		const res = await deleteFile({
			path: activeFile.value.path,
			type: activeFile.value.ext,
		});
		if (res.status) {
			paging.value.reload();
			pageContainer.value.notify.success(res.msg);
		} else {
			pageContainer.value.notify.error(res.msg);
		}
		close && close();
		uni.hideLoading();
		setTimeout(() => {
			hideContextMenu();
		}, 200);
	}
};

// 新建文件夹或文件
export const openCreateFile = (type = 'folder') => {
	hideNavMenu();
	createFileType.value = type;
	showCreateDialog.value = true;
};

// 确认新建文件夹或文件
export const confirmCreate = async (close) => {
	if (createFileName.value === '') {
		pageContainer.value.notify.error($t('files.nameEmpty'));
		return;
	}
	if (matchUnqualifiedString(createFileName.value)) {
		pageContainer.value.notify.error($t('files.invalidChars'));
		return;
	}
	try {
		uni.showLoading({
			title: $t(createFileType.value === 'folder' ? 'files.creatingFolder' : 'files.creatingFile'),
		});
		const path = `${currentPath.value}/${createFileName.value}`;
		const res = await createNewFile({
			path,
			type: createFileType.value,
		});
		if (res.status) {
			paging.value.reload();
			pageContainer.value.notify.success(res.msg);
		} else {
			pageContainer.value.notify.error(res.msg);
		}
		close && close();
		createFileName.value = '';
	} catch (error) {
		console.error(error);
	} finally {
		uni.hideLoading();
	}
};

export const clickActionSheet = () => {
	actionSheet.value.open();
};

export const handleActionSheet = ({ index }) => {
	switch (index) {
		case 0:
			openCreateFile('folder');
			break;
		case 1:
			openCreateFile('file');
			break;
	}
};
