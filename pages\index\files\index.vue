<template>
  <page-container ref="pageContainer" :title="$t('home.fileManagement')" :is-back="false" :has-tab-bar="true">
    <ServerSelector v-if="serverList.length" @select="onSelect" @delete="onDelete" />
    <view class="p-20" v-else>
      <EmptyServerList @scan="startScan" />
    </view>
  </page-container>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import PageContainer from '@/components/PageContainer/index.vue';
  import ServerSelector from '@/components/ServerSelector/index.vue';
  import EmptyServerList from '@/pages/index/serverList/emptyServerList.vue';
  import { useServerListStore } from '@/pages/index/serverList/store';
  import { $t } from '@/locale/index.js';
  import { getServerList, navigateServerInfo, onActionDelete } from '@/pages/index/serverList/useController';

  const { serverList } = useServerListStore().getReactiveState();
  const pageContainer = ref(null);
  const startScan = () => {
    uni.$emit('scan');
  };

  const onDelete = async (item) => {
    await onActionDelete(item);
    pageContainer.value.notify.success($t('common.deleteSuccess'));
  };

  const onSelect = (item) => {
    navigateServerInfo(item, () => {
      uni.navigateTo({
        url: `/linux/files/index`,
        animationType: 'zoom-fade-out',
      });
    });
  };
</script>
