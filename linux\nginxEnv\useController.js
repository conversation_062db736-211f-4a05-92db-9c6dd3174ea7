import { ref, reactive, computed, nextTick } from 'vue';

// 页面引用
export const pageContainer = ref(null);

// 版本选择器引用
let versionPicker = ref(null);

// 设置版本选择器引用的函数
export const setVersionPickerRef = (ref) => {
	versionPicker = ref;
};

// 服务状态
export const serviceStatus = reactive({
	isRunning: true,
	version: 'nginx 1.24',
});

export const serviceLoading = ref(false);

// 负载状态相关
export const isLoadStatusExpanded = ref(false);
export const loadStatusLoading = ref(false);

// 负载状态数据
export const loadStats = reactive({
	activeConnections: 1,
	accepts: 100,
	handled: 100,
	requests: 100,
	reading: 0,
	writing: 1,
	waiting: 0,
	worker: 4,
	workerCpu: 0.01,
	workerMem: '139MB',
});

// 快速操作配置 - 动态计算
export const quickActions = computed(() => {
	const actions = [];

	if (serviceStatus.isRunning) {
		// 服务运行时显示：停止、重启、重载配置
		actions.push(
			{
				id: 'stop',
				title: '停止',
				icon: 'pause-circle-fill',
				useIconfont: false,
			},
			{
				id: 'restart',
				title: '重启',
				icon: 'reload',
				useIconfont: false,
			},
			{
				id: 'reload',
				title: '重载配置',
				icon: 'checkmark-circle',
				useIconfont: false,
			},
		);
	} else {
		// 服务停止时显示：启动
		actions.push({
			id: 'start',
			title: '启动',
			icon: 'play-circle-fill',
			useIconfont: false,
		});
	}

	return actions;
});

// 功能模块配置 - 根据PC端截图的6个功能
export const functionModules = ref([
	{
		id: 'config',
		title: '配置文件',
		desc: '编辑nginx.conf主配置文件',
		icon: 'file-text',
		useIconfont: false,
		showData: false,
		navigatable: true, // 标识此模块可跳转
	},
	{
		id: 'version',
		title: '版本切换',
		desc: '切换不同的nginx版本',
		icon: 'tags',
		useIconfont: false,
		showData: true,
		dataValue: 'nginx 1.24',
		dataUnit: '',
	},
	{
		id: 'status',
		title: '负载状态',
		desc: '查看连接数和请求统计',
		icon: 'grid',
		useIconfont: false,
		showData: true,
		dataValue: '1',
		dataUnit: '个活跃连接',
	},
	{
		id: 'performance',
		title: '性能调整',
		desc: '调整worker进程和连接数等参数',
		icon: 'setting',
		useIconfont: false,
		showData: false,
		navigatable: true, // 标识此模块可跳转
	},
	{
		id: 'errorLog',
		title: '错误日志',
		desc: '查看nginx错误日志',
		icon: 'warning',
		useIconfont: false,
		showData: false,
		navigatable: true, // 标识此模块可跳转
	},
]);

// 版本列表
export const availableVersions = ref([
	'nginx 1.24',
	'nginx 1.26',
	'nginx 1.22',
	'nginx 1.28',
	'nginx -Tengine3.1',
	'nginx 1.21',
	'nginx openresty',
	'nginx 1.20',
]);

// 版本选择器列数据 - 转换为picker组件需要的格式
export const versionColumns = computed(() => {
	return [availableVersions.value];
});

// 初始化数据
export const initNginxEnvData = async () => {
	try {
		// 模拟API调用获取服务状态
		await mockGetServiceStatus();
		// 模拟API调用获取负载状态数据
		await mockGetLoadStatus();
		// 模拟API调用获取Nginx负载状态
		await mockGetNginxLoadStatus();
	} catch (error) {
		console.error('初始化Nginx环境数据失败:', error);
	}
};

// 模拟API - 获取服务状态
const mockGetServiceStatus = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 模拟随机状态
			const isRunning = Math.random() > 0.3;
			serviceStatus.isRunning = isRunning;
			resolve();
		}, 500);
	});
};

// 模拟API - 获取负载状态数据
const mockGetLoadStatus = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 更新负载状态模块的数据
			const statusModule = functionModules.value.find((m) => m.id === 'status');
			if (statusModule) {
				const connections = Math.floor(Math.random() * 10) + 1;
				statusModule.dataValue = connections.toString();
				statusModule.dataUnit = '个活跃连接';
			}
			resolve();
		}, 300);
	});
};

// 服务控制
export const toggleService = async (value) => {
	try {
		serviceLoading.value = true;
		// 模拟API调用
		await new Promise((resolve) => setTimeout(resolve, 1000));

		serviceStatus.isRunning = value;
		if (value) {
			pageContainer.value?.notify?.success('Nginx服务启动成功');
		} else {
			pageContainer.value?.notify?.success('Nginx服务停止成功');
		}
	} catch (error) {
		console.error('服务状态切换失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	} finally {
		serviceLoading.value = false;
	}
};

// 快速操作处理
export const handleQuickAction = async (action) => {
	try {
		switch (action.id) {
			case 'start':
				await toggleService(true);
				break;
			case 'stop':
				await toggleService(false);
				break;
			case 'restart':
				pageContainer.value?.notify?.info('正在重启Nginx服务...');
				serviceLoading.value = true;
				await new Promise((resolve) => setTimeout(resolve, 2000));
				// 重启后确保服务是运行状态
				serviceStatus.isRunning = true;
				await mockGetLoadStatus();
				serviceLoading.value = false;
				pageContainer.value?.notify?.success('Nginx服务重启成功');
				break;
			case 'reload':
				pageContainer.value?.notify?.info('正在重载配置...');
				serviceLoading.value = true;
				await new Promise((resolve) => setTimeout(resolve, 1000));
				serviceLoading.value = false;
				pageContainer.value?.notify?.success('配置重载成功');
				break;
		}
	} catch (error) {
		console.error('快速操作失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
		serviceLoading.value = false;
	}
};

// 模块点击处理
export const handleModuleClick = async (module) => {
	try {
		switch (module.id) {
			case 'config':
				// 直接跳转到nginx配置文件编辑器
				openNginxConfigEditor();
				break;
			case 'version':
				// 显示版本选择
				showVersionSelector();
				break;
			case 'status':
				// 切换负载状态展开状态
				isLoadStatusExpanded.value = !isLoadStatusExpanded.value;
				if (isLoadStatusExpanded.value) {
					// 展开时刷新数据
					await refreshLoadStatus();
				}
				break;
			case 'performance':
				// 导航到性能调整页面
				uni.navigateTo({
					url: '/linux/nginxEnv/performance/index',
				});
				break;
			case 'errorLog':
				// 导航到错误日志页面
				uni.navigateTo({
					url: '/linux/nginxEnv/errorLog/index',
				});
				break;
		}
	} catch (error) {
		console.error('模块操作失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	}
};

// 显示版本选择器
const showVersionSelector = () => {
	// 设置当前版本的默认选中索引
	const currentVersionIndex = availableVersions.value.findIndex((version) => version === serviceStatus.version);
	if (currentVersionIndex !== -1) {
		// 使用 nextTick 确保组件已经渲染完成
		nextTick(() => {
			versionPicker.value?.setIndexs([currentVersionIndex], true);
		});
	}
	versionPicker.value?.open();
};

// 版本选择确认回调
export const onVersionConfirm = async (e) => {
	const selectedVersion = e.value[0];
	if (selectedVersion !== serviceStatus.version) {
		try {
			pageContainer.value?.notify?.info(`正在切换到 ${selectedVersion}...`);
			await new Promise((resolve) => setTimeout(resolve, 2000));

			serviceStatus.version = selectedVersion;
			// 更新版本模块显示
			const versionModule = functionModules.value.find((m) => m.id === 'version');
			if (versionModule) {
				versionModule.dataValue = selectedVersion;
			}

			pageContainer.value?.notify?.success(`已成功切换到 ${selectedVersion}`);
		} catch (error) {
			console.error('版本切换失败:', error);
			pageContainer.value?.notify?.error('版本切换失败，请重试');
		}
	} else {
		pageContainer.value?.notify?.info('当前已是此版本');
	}
};

// 版本选择取消回调
export const onVersionCancel = () => {
	// 取消选择时的处理，可以为空或添加一些提示
};

// 刷新负载状态
export const refreshLoadStatus = async () => {
	try {
		loadStatusLoading.value = true;
		// 模拟API调用获取最新负载数据
		await mockGetNginxLoadStatus();
		pageContainer.value?.notify?.success('负载状态已更新');
	} catch (error) {
		console.error('刷新负载状态失败:', error);
		pageContainer.value?.notify?.error('刷新失败，请重试');
	} finally {
		loadStatusLoading.value = false;
	}
};

// 初始化Nginx负载状态（用于负载状态详情页）
export const initNginxLoadStatus = async () => {
	try {
		await mockGetNginxLoadStatus();
	} catch (error) {
		console.error('初始化Nginx负载状态失败:', error);
	}
};

// 模拟API - 获取Nginx负载状态
const mockGetNginxLoadStatus = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 模拟动态数据
			loadStats.activeConnections = Math.floor(Math.random() * 10) + 1;
			loadStats.accepts = Math.floor(Math.random() * 200) + 100;
			loadStats.handled = Math.floor(Math.random() * 200) + 100;
			loadStats.requests = Math.floor(Math.random() * 300) + 100;
			loadStats.reading = Math.floor(Math.random() * 5);
			loadStats.writing = Math.floor(Math.random() * 5) + 1;
			loadStats.waiting = Math.floor(Math.random() * 10);
			loadStats.worker = Math.floor(Math.random() * 8) + 2;
			loadStats.workerCpu = (Math.random() * 2).toFixed(2);
			loadStats.workerMem = Math.floor(Math.random() * 200 + 100) + 'MB';
			resolve();
		}, 800);
	});
};

// 打开nginx配置文件编辑器
export const openNginxConfigEditor = async () => {
	try {
		// TODO: 这里需要根据实际API获取nginx配置文件信息
		// 暂时使用模拟数据，后续替换为真实API调用
		const configFileItem = {
			path: '/etc/nginx/nginx.conf', // 实际路径需要从API获取
			title: 'nginx.conf',
			size: 1024, // 实际大小需要从API获取
		};

		// 检查文件大小限制（3MB）
		if (configFileItem.size > 3 * 1024 * 1024) {
			pageContainer.value?.notify?.warning('配置文件过大，无法编辑');
			return;
		}

		// 跳转到文件编辑器
		uni.navigateTo({
			url: `/linux/files/editor/index?fileItem=${JSON.stringify(configFileItem)}`,
			animationType: 'zoom-fade-out',
		});
	} catch (error) {
		console.error('打开配置文件编辑器失败:', error);
		pageContainer.value?.notify?.error('打开配置文件失败，请重试');
	}
};
