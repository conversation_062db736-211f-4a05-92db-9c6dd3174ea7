<template>
  <z-paging
    ref="domainPaging"
    class="mt-270 p-20"
    :default-page-size="100"
    use-virtual-list
    :force-close-inner-list="true"
    :auto-hide-loading-after-first-loaded="false"
    :auto-show-system-loading="true"
    @virtualListChange="virtualListChange"
    @query="queryList"
    @refresherStatusChange="reload"
    :refresher-complete-delay="200"
  >
    <view
      v-for="item in domainList"
      :id="`zp-id-${item.zp_index}`"
      :key="item.zp_index"
      class="p-30 rd-14 mb-20 bg-primar website-item flex justify-between items-center"
    >
      <view>
        <view class="text-bt-primary text-26 font-bold w-300 word-break">{{ item.name }}</view>
      </view>
      <view>
        <view class="text-secondary text-26 max-w-200 word-break">{{ $t('website.manage.port') }}{{ item.port }}</view>
      </view>
      <view class="flex items-center" v-if="domainList.length > 1" @click="handleDeleteDomain(item)">
        <uni-icons type="trash-filled" color="var(--text-color-secondary)" size="24" />
        <text class="text-26 text-secondary">{{ $t('website.delete') }}</text>
      </view>
      <view class="flex items-center" v-else>
        <text class="text-26 text-secondary">{{ $t('website.manage.cannotBeOperated') }}</text>
      </view>
    </view>
  </z-paging>
  <CustomDialog
    contentHeight="200rpx"
    v-model="isShowDeleteDomain"
    :title="$t('website.warning')"
    :confirmText="$t('website.delete')"
    :loading="domainLoading"
    :confirmStyle="{
      backgroundColor: '#FF3B30',
    }"
    @confirm="confirmDeleteDomain"
  >
    <view class="text-secondary flex justify-center items-center h-full">
      {{ $t('website.confirmDelete', { name: currentDomain?.name }) }}
    </view>
  </CustomDialog>
</template>

<script setup>
  import { ref } from 'vue';
  import { onUnload } from '@dcloudio/uni-app';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import { $t } from '@/locale/index.js';
  import {
    pageContainer,
    domainPaging,
    getDomainList,
    handleDeleteDomain,
    currentDomain,
    isShowDeleteDomain,
    confirmDeleteDomain,
    domainLoading,
  } from './useController';

  const domainList = ref([]);

  const virtualListChange = (vList) => {
    domainList.value = vList;
  };

  const queryList = async (page, pageSize) => {
    try {
      const res = await getDomainList(page, pageSize);
      domainPaging.value.complete(res);
      domainPaging.value.updateVirtualListRender();
    } catch (error) {
      domainPaging.value.complete([]);
      console.error(error);
    }
  };

  const reload = (reloadType) => {
    if (reloadType === 'complete') {
      pageContainer.value.notify.success($t('website.refreshSuccess'));
    }
  };

  onUnload(() => {
    isShowDeleteDomain.value = false;
  });
</script>

<style scoped>
  .website-item {
    border: 2rpx solid var(--color-border, #eee);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  }
</style>
