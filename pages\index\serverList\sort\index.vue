<template>
	<view class="sort-page" :style="{ backgroundColor: theme === 'dark' ? '#1a1a1a' : '#ffffff' }">
		<!-- 自定义导航栏 -->
		<custom-nav :is-back="true" :bg-color="theme === 'dark' ? '#1a1a1a' : '#f8f8f8'">
			<template #nav-title>
				<view class="nav-title" :style="{ color: theme === 'dark' ? '#ffffff' : '#333333' }">
					<text class="nav-title-text">服务器排序</text>
				</view>
			</template>
			<template #right>
				<view class="nav-right-btn">
					<text class="save-btn" :style="{ color: theme === 'dark' ? '#cccccc' : '#666666' }" @tap="handleSave">确认</text>
				</view>
			</template>
		</custom-nav>

		<!-- 提示信息 -->
		<view class="tip-container" :style="{ backgroundColor: theme === 'dark' ? 'rgba(23, 132, 7, 0.1)' : 'rgba(32, 165, 58, 0.1)' }">
			<view class="tip-text" :style="{ color: theme === 'dark' ? '#cccccc' : '#666666' }">轻触卡片即可拖拽调整服务器顺序</view>
		</view>

		<!-- 拖拽排序列表容器 -->
		<view class="drag-sort-wrapper">
			<view
				id="sortable-list"
				class="sortable-container"
				:change:list="renderScript.updateList"
				:list="sortableServerList"
				:change:navHeight="renderScript.updateNavHeight"
				:navHeight="navHeight"
			>
				<view
					v-for="(server, index) in sortableServerList"
					:key="server.uniqueKey || `server_${server.ip}_${index}`"
					class="sortable-item"
					:data-index="index"
				>
					<view class="server-card" :style="{ backgroundColor: theme === 'dark' ? '#2c2c2c' : '#fff' }">
						<view class="server-info">
							<view class="server-status-dot" :class="{ online: server.status }" :style="{ backgroundColor: server.status ? (theme === 'dark' ? '#178407' : '#20a50a') : '#f44336' }"></view>
							<view class="server-details">
								<text class="server-name" :style="{ color: theme === 'dark' ? '#ffffff' : '#333333' }">{{ server.name }}</text>
								<text class="server-ip" :style="{ color: theme === 'dark' ? '#999999' : '#999999' }">{{ formatIPDisplay(server.ip) }}</text>
							</view>
							<view class="server-status-text">
								<text :class="{ online: server.status }" :style="{ color: server.status ? (theme === 'dark' ? '#178407' : '#20a50a') : '#f44336' }">
									{{ server.status ? '在线' : '离线' }}
								</text>
							</view>
						</view>
						<view class="drag-handle">
							<text class="drag-icon" :style="{ color: theme === 'dark' ? '#999999' : '#999999' }">⋮⋮</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 通知组件 -->
		<uv-notify ref="notify" safeAreaInsetTop></uv-notify>
	</view>
</template>

<script setup>
	import { ref, onMounted, onUnmounted, watch, nextTick, getCurrentInstance } from 'vue';
	import { onBackPress } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import uvNotify from '@/uni_modules/uv-notify/components/uv-notify/uv-notify.vue';
	import { useServerListStore } from '../store';
	import { formatIPDisplay } from '../useController';
	import { useConfigStore } from '@/store';
	import { getTheme } from '@/hooks/useTheme';

	const theme = getTheme();

	const notify = ref(null);

	// 获取服务器列表数据和配置数据
	const { serverList } = useServerListStore().getReactiveState();
	const { configList } = useConfigStore().getReactiveState();

	// 可排序的服务器列表
	const sortableServerList = ref([]);

	// 导航栏高度（用于滚动边界计算）
	const navHeight = ref(0);

	// 初始化数据
	const initializeData = () => {
		// 直接使用服务器列表数据，保持原始结构
		sortableServerList.value = [...serverList.value];
	};

	// 计算导航栏高度
	const calculateNavHeight = () => {
		uni.getSystemInfo({
			success: (res) => {
				// 状态栏高度 + 导航栏高度 + 提示区域高度
				const statusBarHeight = res.statusBarHeight || 0;
				const navBarHeight = 44; // 导航栏固定高度 (88rpx / 2)
				const tipHeight = 80; // 提示区域大概高度
				navHeight.value = statusBarHeight + navBarHeight + tipHeight;
			},
		});
	};

	// 处理来自 renderJs 的排序更新
	const handleSortUpdate = (newOrder) => {
		if (newOrder && Array.isArray(newOrder)) {
			sortableServerList.value = [...newOrder];
			// 添加震动反馈
			uni.vibrateShort();
		}
	};

	// 获取当前组件实例，用于暴露方法给 renderJs
	const instance = getCurrentInstance();
	if (instance) {
		// 将方法暴露到组件实例上，这样 renderJs 就可以通过 callMethod 调用
		instance.ctx.handleSortUpdate = handleSortUpdate;
		instance.proxy.handleSortUpdate = handleSortUpdate;
	}

	// 保存排序
	const handleSave = () => {
		// 更新原始服务器列表的顺序
		serverList.value = [...sortableServerList.value];

		// 同步更新 configList 的顺序
		const newConfigOrder = [];
		sortableServerList.value.forEach((server) => {
			const config = configList.value.find((c) => c.panelPath === server.rawPath);
			if (config) {
				newConfigOrder.push(config);
			}
		});

		// 添加任何不在 serverList 中的配置项
		configList.value.forEach((config) => {
			if (!newConfigOrder.find((c) => c.panelPath === config.panelPath)) {
				newConfigOrder.push(config);
			}
		});

		// 更新 configList 的顺序
		configList.value = newConfigOrder;

		// 保存排序到本地存储 - 使用 rawPath 作为更稳定的标识符
		const sortOrder = sortableServerList.value.map((server) => server.rawPath);
		uni.setStorageSync('serverSortOrder', sortOrder);

		// 保存 configList 到本地存储
		uni.setStorageSync('configList', configList.value);

		// 显示成功提示
		notify.value.success('排序已保存');

		// 设置一个标记，表示排序已更改，需要重新渲染
		uni.setStorageSync('sortOrderChanged', true);

		// 延迟返回上一页
		setTimeout(() => {
			uni.navigateBack();
		}, 1000);
	};

	// 处理返回按钮
	onBackPress(() => {
		return false;
	});

	// 组件卸载时清理
	onUnmounted(() => {
		// 清理工作（如果需要的话）
	});

	// 暴露方法给 renderJs 调用
	defineExpose({
		handleSortUpdate,
	});

	onMounted(() => {
		initializeData();
		calculateNavHeight();

		// 延迟一下确保 renderJs 能接收到数据
		setTimeout(() => {
			// 强制触发一次数据更新，确保 renderJs 接收到数据
			const currentData = [...sortableServerList.value];
			sortableServerList.value = [];
			nextTick(() => {
				sortableServerList.value = currentData;
			});
		}, 500);
	});

	// 监听服务器列表变化，同步更新排序列表
	watch(
		serverList,
		() => {
			if (sortableServerList.value.length === 0) {
				initializeData();
			}
		},
		{ immediate: true },
	);

	// 监听导航栏高度变化，传递给renderJs
	watch(
		navHeight,
		(newHeight) => {
			// 通过renderJs的updateNavHeight方法更新高度
			// 这里需要等待DOM更新后再调用
			nextTick(() => {
				// renderJs会自动接收这个变化
			});
		},
		{ immediate: true },
	);
</script>

<!-- renderJs 脚本 - 在视图层运行，可以直接操作 DOM -->
<script module="renderScript" lang="renderjs">
	import Sortable from 'sortablejs'

	let sortableInstance = null;
	let currentList = [];
	let customNavHeight = 0;
	let scrollTimer = null; // 滚动防抖定时器
	let userInteracted = false; // 用户是否已经交互过

	export default {
		mounted() {
			this.initSortable();
			// 监听用户首次交互，启用震动功能
			this.enableVibrateOnInteraction();
		},

		methods: {
			// 启用震动功能（需要用户交互后才能使用）
			enableVibrateOnInteraction() {
				const enableVibrate = () => {
					userInteracted = true;
					document.removeEventListener('touchstart', enableVibrate);
					document.removeEventListener('click', enableVibrate);
				};
				document.addEventListener('touchstart', enableVibrate, { once: true });
				document.addEventListener('click', enableVibrate, { once: true });
			},

			// 安全的震动函数
			safeVibrate(duration = 50) {
				if (userInteracted && navigator.vibrate) {
					try {
						navigator.vibrate(duration);
					} catch (error) {
						console.warn('震动功能不可用:', error);
					}
				}
			},

			// 防止触摸移动的默认行为（解决被动事件监听器警告）
			preventTouchMove(e) {
				// 只在拖拽过程中阻止默认行为
				if (document.body.classList.contains('dragging')) {
					if (e.cancelable && !e.defaultPrevented) {
						e.preventDefault();
					}
				}
			},

			// 初始化 SortableJS
			initSortable() {
				const container = document.getElementById('sortable-list');
				if (!container) {
					console.error('找不到排序容器');
					return;
				}

				// 输出页面滚动信息用于调试
				const windowHeight = window.innerHeight;
				const documentHeight = document.documentElement.scrollHeight;
				const maxScroll = documentHeight - windowHeight;
				console.log('页面滚动信息:', { windowHeight, documentHeight, maxScroll, canScroll: maxScroll > 0 });

				// 销毁已存在的实例
				if (sortableInstance) {
					sortableInstance.destroy();
				}

				// 创建新的 SortableJS 实例
				sortableInstance = Sortable.create(container, {
					// 动画优化 - 更流畅的动画
					animation: 200, // 增加动画时间，让移动更平滑
					easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)", // 更自然的缓动函数

					// 长按触发优化 - 更快响应
					delay: 150, // 减少长按延迟，提高响应速度
					delayOnTouchStart: true, // 在触摸开始时应用延迟
					touchStartThreshold: 5, // 降低触摸开始阈值，提高灵敏度

					// 移除拖拽手柄限制，整个卡片都可以拖拽
					// handle: '.drag-handle', // 注释掉，让整个项目都可拖拽

					// 拖拽时的样式类
					ghostClass: 'sortable-ghost',
					chosenClass: 'sortable-chosen',
					dragClass: 'sortable-drag',

					// 强制回退到原生 HTML5 拖拽，提供更好的移动端体验
					forceFallback: true,
					fallbackClass: 'sortable-fallback',
					fallbackOnBody: true,
					fallbackTolerance: 3, // 降低拖拽容忍度，提高响应性

					// 自动滚动配置 - 关键优化
					scroll: true, // 启用自动滚动
					scrollSensitivity: 80, // 滚动敏感度，距离边缘80px开始滚动
					scrollSpeed: 12, // 滚动速度，配合自定义滚动逻辑
					bubbleScroll: true, // 允许冒泡滚动到父元素
					scrollFn: (_, offsetY) => {
						// 使用自定义滚动函数，提供更平滑的滚动体验
						const scrollContainer = window;
						const scrollTop = scrollContainer.pageYOffset || document.documentElement.scrollTop;
						const maxScroll = document.documentElement.scrollHeight - window.innerHeight;

						console.log('SortableJS scrollFn 调用:', { offsetY, scrollTop, maxScroll });

						// 清除之前的滚动定时器
						if (scrollTimer) {
							clearTimeout(scrollTimer);
						}

						// 使用防抖机制，避免过于频繁的滚动
						scrollTimer = setTimeout(() => {
							// 限制滚动范围，防止过度滚动
							if (offsetY > 0 && scrollTop < maxScroll) {
								// 向下滚动
								const scrollAmount = Math.min(offsetY, maxScroll - scrollTop);
								console.log('SortableJS 向下滚动:', scrollAmount);
								scrollContainer.scrollBy(0, scrollAmount);
							} else if (offsetY < 0 && scrollTop > 0) {
								// 向上滚动
								const scrollAmount = Math.max(offsetY, -scrollTop);
								console.log('SortableJS 向上滚动:', scrollAmount);
								scrollContainer.scrollBy(0, scrollAmount);
							}
						}, 16); // 约60fps的滚动频率
					},

					// 交换阈值优化
					swapThreshold: 0.65, // 交换阈值，更容易触发位置交换
					invertSwap: false,
					direction: 'vertical', // 明确指定垂直方向

					// 解决被动事件监听器警告的关键配置
					preventOnFilter: false, // 防止在过滤时阻止默认行为
					removeCloneOnHide: true, // 隐藏时移除克隆元素

					// 事件回调
					onStart: (evt) => {
						console.log('开始拖拽:', evt.oldIndex);
						// 添加拖拽开始的视觉反馈
						document.body.classList.add('dragging');

						// 注意：不要禁用页面滚动，因为我们需要自动滚动功能
						// 只阻止触摸事件的默认行为来防止意外的页面交互
						try {
							// 阻止触摸事件的默认行为
							document.addEventListener('touchmove', this.preventTouchMove, { passive: false });
						} catch (error) {
							console.warn('设置拖拽样式时出错:', error);
						}

						// 添加触觉反馈
						this.safeVibrate(50);
					},

					onMove: (evt) => {
						// 简化的自动滚动边界检测
						const rect = evt.draggedRect;
						const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
						const windowHeight = window.innerHeight;
						const documentHeight = document.documentElement.scrollHeight;
						const maxScroll = documentHeight - windowHeight;

						// 滚动敏感区域大小（增大以便更容易触发）
						const scrollZone = 150;
						const scrollSpeed = 8;

						// 计算拖拽元素相对于视口的位置
						const draggedTop = rect.top - scrollTop;
						const draggedBottom = rect.bottom - scrollTop;

						console.log('拖拽位置信息:', {
							draggedTop,
							draggedBottom,
							windowHeight,
							scrollTop,
							maxScroll,
							customNavHeight,
							scrollZone
						});

						// 向上滚动：拖拽元素接近顶部
						if (draggedTop < scrollZone && scrollTop > 0) {
							console.log('触发向上滚动');
							window.scrollBy(0, -scrollSpeed);
						}
						// 向下滚动：拖拽元素接近底部
						else if (draggedBottom > windowHeight - scrollZone && scrollTop < maxScroll) {
							console.log('触发向下滚动');
							window.scrollBy(0, scrollSpeed);
						}

						return true;
					},

					onEnd: (evt) => {
						console.log('拖拽结束:', evt.oldIndex, '->', evt.newIndex);
						document.body.classList.remove('dragging');

						// 清理事件监听器
						try {
							// 移除触摸事件监听器
							document.removeEventListener('touchmove', this.preventTouchMove);
						} catch (error) {
							console.warn('清理事件监听器时出错:', error);
						}

						// 如果位置发生了变化，更新数据
						if (evt.oldIndex !== evt.newIndex) {
							this.updateOrder(evt.oldIndex, evt.newIndex);

							// 添加完成反馈
							this.safeVibrate(30);
						}
					}
				});


			},

			// 更新排序
			updateOrder(oldIndex, newIndex) {
				if (!currentList || currentList.length === 0) {
					console.warn('当前列表为空，无法更新排序');
					return;
				}

				// 创建新的排序数组
				const newList = [...currentList];
				const [movedItem] = newList.splice(oldIndex, 1);
				newList.splice(newIndex, 0, movedItem);

				// 更新当前列表
				currentList = newList;

				// 通知 Vue 层数据变化
				try {
					this.$ownerInstance.callMethod('handleSortUpdate', newList);
				} catch (error) {
					console.error('callMethod 调用失败:', error);
				}
			},

			// 接收来自 Vue 层的数据更新
			updateList(newList) {
				currentList = newList || [];

				// 如果 SortableJS 还未初始化，延迟初始化
				if (!sortableInstance) {
					this.$nextTick(() => {
						this.initSortable();
					});
				}
			},

			// 接收来自 Vue 层的导航栏高度更新
			updateNavHeight(height) {
				customNavHeight = height || 0;
				console.log('导航栏高度更新:', customNavHeight);
			}
		},

		beforeDestroy() {
			// 清理 SortableJS 实例
			if (sortableInstance) {
				sortableInstance.destroy();
				sortableInstance = null;
			}

			// 清理滚动定时器
			if (scrollTimer) {
				clearTimeout(scrollTimer);
				scrollTimer = null;
			}

			// 清理拖拽状态和事件监听器
			document.body.classList.remove('dragging');
			try {
				document.removeEventListener('touchmove', this.preventTouchMove);
			} catch (error) {
				console.warn('清理事件监听器时出错:', error);
			}
		}
	}
</script>

<style scoped>
	.sort-page {
		min-height: 100vh;
		width: 100%;
		position: relative;
	}

	.nav-right-btn {
		margin-right: 20rpx;
	}

	.save-btn {
		font-size: 32rpx;
		font-weight: 500;
	}

	.save-btn:active {
		opacity: 0.7;
	}

	.tip-container {
		padding: 30rpx 40rpx;
		margin: 20rpx;
		border-radius: 12rpx;
	}

	.tip-text {
		font-size: 28rpx;
		text-align: center;
		margin-bottom: 8rpx;
	}

	.tip-subtext {
		font-size: 24rpx;
		text-align: center;
		opacity: 0.8;
	}

	.drag-sort-wrapper {
		padding: 0 20rpx;
		margin-top: 20rpx;
		position: relative;
	}

	.sortable-container {
		width: 100%;
	}

	.sortable-item {
		margin-bottom: 16rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
	}

	.server-card {
		padding: 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.server-info {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.server-status-dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.server-details {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.server-name {
		font-size: 32rpx;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.server-ip {
		font-size: 26rpx;
	}

	.server-status-text {
		margin-right: 20rpx;
	}

	.server-status-text text {
		font-size: 24rpx;
	}

	.drag-handle {
		padding: 16rpx;
		cursor: grab;
		user-select: none;
		touch-action: none;
		-webkit-touch-callout: none;
		-webkit-user-select: none;
	}

	.drag-handle:active {
		cursor: grabbing;
	}

	.drag-icon {
		font-size: 32rpx;
		font-weight: bold;
		line-height: 1;
	}

	/* SortableJS 拖拽状态样式 - 优化版 */
	.sortable-ghost {
		opacity: 0.3;
		background-color: rgba(32, 165, 58, 0.15);
		border: 2rpx dashed #20a50a;
		transform: scale(0.98);
		transition: all 0.2s ease;
	}

	.sortable-chosen {
		transform: scale(1.03) translateZ(0);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.25);
		z-index: 1000;
		transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.sortable-drag {
		transform: scale(1.06) translateZ(0);
		box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.35);
		border: 2rpx solid #20a50a;
		z-index: 1001;
		transition: none !important;
	}

	.sortable-fallback {
		opacity: 0.9;
		transform: scale(1.06) translateZ(0);
		box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.35);
		border: 2rpx solid #20a50a;
		border-radius: 12rpx;
		z-index: 1001;
		transition: none !important;
	}

	/* 拖拽过程中的全局样式 - 优化版 */
	body.dragging {
		user-select: none;
		-webkit-user-select: none;
		-webkit-touch-callout: none;
		-webkit-tap-highlight-color: transparent;
		/* 注意：不设置 overflow: hidden，因为我们需要自动滚动功能 */
	}

	body.dragging * {
		cursor: grabbing !important;
		pointer-events: none; /* 防止拖拽时触发其他元素的事件 */
	}

	body.dragging .sortable-item {
		pointer-events: auto; /* 恢复拖拽项的事件 */
	}

	/* 拖拽手柄优化 */
	.drag-handle {
		transition: all 0.2s ease;
	}

	.drag-handle:hover {
		background-color: rgba(32, 165, 58, 0.1);
		border-radius: 8rpx;
		transform: scale(1.1);
	}

	.drag-handle:active {
		transform: scale(0.95);
	}

	/* 拖拽项的悬停效果 - 优化版 */
	.sortable-item {
		transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		will-change: transform, box-shadow;
	}

	.sortable-item:hover:not(.sortable-chosen):not(.sortable-drag) {
		transform: translateY(-4rpx) translateZ(0);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.18);
	}

	/* 拖拽时禁用过渡动画 */
	.sortable-item.sortable-chosen,
	.sortable-item.sortable-drag {
		transition: none !important;
	}

	/* 拖拽完成后恢复过渡动画 */
	.sortable-item:not(.sortable-chosen):not(.sortable-drag) {
		transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	/* 整个卡片可拖拽的视觉提示 */
	.server-card {
		cursor: grab;
		transition: all 0.2s ease;
		touch-action: manipulation; /* 优化触摸响应 */
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;
	}

	.server-card:active {
		cursor: grabbing;
	}

	/* 优化拖拽容器 */
	.sortable-container {
		position: relative;
		z-index: 1;
		/* 优化触摸性能 */
		-webkit-overflow-scrolling: touch;
		touch-action: pan-y; /* 只允许垂直滚动 */
	}

	/* 全局触摸优化 */
	.sort-page {
		/* 防止触摸时的高亮效果 */
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
	}
</style>
