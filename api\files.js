import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

// 获取文件列表
export const getFileList = (data) => {
  const url = DEFAULT_API_TYPE === 'domestic' ? '/files?action=GetDirNew' : '/files?action=GetDir';
  return axios(url, data);
};

/**
 * @description 获取文件内容
 * @returns { Promise }
 */
export const getFileBody = (data) => {
  const url = '/files?action=GetFileBody';
  return axios(url, data);
};

/**
 * @description 保存文件内容
 * @returns { Promise }
 */
export const saveFileBody = (data) => {
  const url = '/files?action=SaveFileBody';
  return axios(url, data);
};

/**
 * @description 删除文件
 * @returns { Promise }
 */
export const deleteFile = (data) => {
  const url = `/files?action=${data.type === 'folder' ? 'DeleteDir' : 'DeleteFile'}`
  return axios(url, { path: data.path });
};

/**
 * @description 文件重命名
 * @param {String} sfile 旧名称路径
 * @param {String} dfile 新名称路径
 * @returns { Promise }
 */
export const setFileName = (data) => {
  const url = '/files?action=MvFile'
  return axios(url, data);
};

/**
 * @description 设置备注
 * @returns { Promise }
 */
export const setFilePs = (data) => {
  const url = '/files?action=set_file_ps'
  return axios(url, data);
};

/**
 * @description 新建文件/文件夹
 * @param {String} path 新建文件/文件夹路径
 * @returns { Promise }
 */
export const createNewFile = (data) => {
  const url = `/files?action=${data.type === 'folder' ? 'CreateDir' : 'CreateFile'}`;
  return axios(url, data);
};
