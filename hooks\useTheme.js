/**
 * 主题管理工具类
 */

// 主题类型
export const THEME_TYPE = {
    LIGHT: 'light',
    DARK: 'dark'
}

// 主题存储的key
const THEME_STORAGE_KEY = 'app_theme'

// 是否跟随系统主题
const FOLLOW_SYSTEM_KEY = 'theme_follow_system'

/**
 * 获取当前主题
 * @returns {string} 当前主题类型
 */
export function getTheme() {
    try {
        let theme = uni.getStorageSync(THEME_STORAGE_KEY)
        const followSystem = uni.getStorageSync(FOLLOW_SYSTEM_KEY)

        if (followSystem && uni.canIUse('getSystemInfoSync')) {
            const systemInfo = uni.getSystemInfoSync()
            theme = systemInfo.theme
        }

        if (!theme) {
            // 默认使用亮色主题
            theme = THEME_TYPE.LIGHT
            setTheme(theme)
        }
        return theme
    } catch (error) {
        console.error('获取主题失败：', error)
        return THEME_TYPE.LIGHT
    }
}

/**
 * 设置主题
 * @param {string} theme 主题类型
 */
export function setTheme(theme) {
    try {
        // 存储主题设置
        uni.setStorageSync(THEME_STORAGE_KEY, theme)

        // 特定于鸿蒙的主题存储
        if (typeof AppStorage !== 'undefined') {
            AppStorage.SetOrCreate('appTheme', theme); // 使用AppStorage机制存储主题
        }

        // 更改全局样式变量
        const pages = getCurrentPages();
        if (pages.length > 0) {
            const currentPage = pages[pages.length - 1];
            if (currentPage) {
                // 使用鸿蒙样式API
                if (typeof currentPage.$page !== 'undefined' && currentPage.$page) {
                    try {
                        // 尝试使用鸿蒙特有API
                        currentPage.$page.setTheme(theme);
                    } catch (e) {
                        console.log('鸿蒙样式API调用失败', e);
                    }
                }

                // 触发页面重新渲染
                setTimeout(() => {
                    uni.$emit('forceRefresh', {});
                }, 50);
            }
        }

        // 设置状态栏
        uni.setNavigationBarColor({
            frontColor: theme === THEME_TYPE.DARK ? '#ffffff' : '#000000',
            backgroundColor: theme === THEME_TYPE.DARK ? '#1a1a1a' : '#ffffff',
            fail: (err) => {
                console.error('设置状态栏颜色失败：', err)
            }
        })

        // 设置系统级底部导航栏颜色
        // #ifdef APP-PLUS
        if (plus && plus.navigator) {
            // 设置系统状态栏
            plus.navigator.setStatusBarStyle(theme === THEME_TYPE.DARK ? 'light' : 'dark');
            plus.navigator.setStatusBarBackground(theme === THEME_TYPE.DARK ? '#2c2c2c' : '#ffffff');

            // 设置系统导航栏
            if (plus.os.name.toLowerCase() === 'android') {
                const barBackground = theme === THEME_TYPE.DARK ? '#2c2c2c' : '#ffffff';
                // 通过安卓原生接口设置导航栏颜色
                try {
                    const mainActivity = plus.android.runtimeMainActivity();
                    const window = mainActivity.getWindow();
                    plus.android.importClass(window);

                    // 设置导航栏颜色
                    const Color = plus.android.importClass("android.graphics.Color");
                    window.setNavigationBarColor(Color.parseColor(barBackground));

                    // 设置导航栏图标颜色（Android 8.0以上支持）
                    if (plus.os.version >= "8.0") {
                        const View = plus.android.importClass("android.view.View");
                        if (theme === THEME_TYPE.LIGHT) {
                            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR);
                        } else {
                            window.getDecorView().setSystemUiVisibility(0);
                        }
                    }
                } catch (e) {
                    console.error('设置系统导航栏颜色失败:', e);
                }
            }
        }
        // #endif

        // 通过 globalData 存储当前主题
        getApp().globalData = getApp().globalData || {}
        getApp().globalData.theme = theme

        // 触发主题变化事件，供页面监听使用
        uni.$emit('themeChange', { theme })

    } catch (error) {
        console.error('设置主题失败：', error)
        uni.showToast({
            title: '主题切换失败',
            icon: 'none'
        })
    }
}

/**
 * 切换主题
 */
export function toggleTheme() {
    const currentTheme = getTheme()
    const newTheme = currentTheme === THEME_TYPE.LIGHT ? THEME_TYPE.DARK : THEME_TYPE.LIGHT
    setTheme(newTheme)
    return newTheme
}

/**
 * 设置是否跟随系统主题
 * @param {boolean} follow 是否跟随系统
 */
export function setFollowSystem(follow) {
    try {
        uni.setStorageSync(FOLLOW_SYSTEM_KEY, follow)
        if (follow && uni.canIUse('getSystemInfoSync')) {
            const systemInfo = uni.getSystemInfoSync()
            setTheme(systemInfo.theme === 'dark' ? THEME_TYPE.DARK : THEME_TYPE.LIGHT)
        }
    } catch (error) {
        console.error('设置跟随系统主题失败：', error)
    }
}

/**
 * 获取是否跟随系统主题
 * @returns {boolean}
 */
export function getFollowSystem() {
    try {
        return !!uni.getStorageSync(FOLLOW_SYSTEM_KEY)
    } catch (error) {
        console.error('获取跟随系统主题设置失败：', error)
        return false
    }
} 