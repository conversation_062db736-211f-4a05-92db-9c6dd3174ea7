<template>
  <page-container ref="pageContainer" :title="$t('firewall.title')">
    <view class="firewall-container">
      <!-- 模糊遮罩 -->
      <view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

      <!-- 悬浮上下文菜单 -->
      <view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
        <view class="menu-item" @click="handleToggleRule">
          <uni-icons
            :type="currentRule?.Strategy === 'accept' ? 'clear' : 'checkbox-filled'"
            size="16"
            :color="currentRule?.Strategy === 'accept' ? '#FF9500' : '#4CD964'"
          ></uni-icons>
          <text class="menu-text">{{ currentRule && currentRule.Strategy === 'accept' ? $t('firewall.block') : $t('firewall.allow') }}</text>
        </view>
        <view class="menu-divider"></view>
        <view class="menu-item menu-delete" @click="showDeleteDialog = true">
          <uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
          <text class="menu-text menu-text-delete">{{ $t('firewall.delete') }}</text>
        </view>
      </view>

      <!-- 克隆项容器 -->
      <view class="fixed-clone-container" v-if="showContextMenu">
        <view :style="clonePosition" v-if="currentRule">
          <view class="rule-item-clone bg-primary">
            <view class="rule-main-info">
              <view class="rule-protocol">{{ currentRule.Protocol }}</view>
              <view class="rule-port"
                >{{ $t('firewall.port') }}：{{ currentRule.Port + (currentRule.Family === 'ipv6' ? $t('firewall.ipv6') : '') }}</view
              >
            </view>
            <view class="flex justify-between">
              <view class="rule-status">
                <text class="info-label">{{ $t('firewall.status') }}:</text>
                <text class="info-value" :class="getStatusClass(currentRule.status)">{{
                  currentRule.status === 0
                    ? $t('firewall.notUsed')
                    : currentRule.status === 1 && !currentRule.Port?.includes('-')
                      ? $t('firewall.externalNetworkDisconnected')
                      : $t('firewall.normal')
                }}</text>
              </view>
              <view class="rule-action">
                <text class="info-label">{{ $t('firewall.strategy') }}:</text>
                <text class="info-value" :class="currentRule.Strategy === 'accept' ? 'status-allow' : 'status-deny'">
                  {{ currentRule.Strategy == 'accept' ? $t('firewall.allow') : $t('firewall.block') }}
                </text>
              </view>
            </view>
            <view class="rule-note mt-10" v-if="currentRule.brief || portsPs[currentRule.Port]"
              >{{ $t('firewall.note') }}：{{ currentRule.brief || portsPs[currentRule.Port] || '' }}</view
            >
          </view>
        </view>
      </view>

      <!-- 开关区域 -->
      <view class="switch-section bg-primary">
        <view class="switch-item">
          <text class="text-primary">{{ $t('firewall.firewallSwitch') }}</text>
          <uv-switch
            v-model="firewallEnabled"
            @change="handleFirewallSwitch"
            activeColor="#20a50a"
            :disabled="loading"
          ></uv-switch>
        </view>
      </view>

      <!-- 端口规则列表 -->
      <z-paging
        ref="rulePaging"
        class="mt-310"
        :default-page-size="100"
        use-virtual-list
        :force-close-inner-list="true"
        :auto-hide-loading-after-first-loaded="false"
        :auto-show-system-loading="true"
        @query="queryRules"
        @virtualListChange="virtualListChange"
        @refresherStatusChange="reload"
        :refresher-complete-delay="200"
      >
        <view class="px-20" v-for="(rule, index) in rulesList" :id="`zp-id-${rule.zp_index}`" :key="rule.zp_index">
          <view
            class="rule-item-container bg-primary mt-20"
            :class="{ disabled: !firewallEnabled }"
            @touchstart="firewallEnabled && handleTouchStart($event)"
            @touchmove="firewallEnabled && handleTouchMove($event)"
            @touchend="firewallEnabled && handleTouchEnd($event)"
            @touchcancel="firewallEnabled && handleTouchCancel($event)"
            :data-index="index"
            :data-rule="JSON.stringify(rule)"
          >
            <view class="rule-main-info">
              <view class="rule-protocol">{{ rule.Protocol }}</view>
              <view class="rule-port">{{ $t('firewall.port') }}：{{ rule.Port + (rule.Family === 'ipv6' ? $t('firewall.ipv6') : '') }}</view>
            </view>
            <view class="flex justify-between">
              <view class="rule-status">
                <text class="info-label">{{ $t('firewall.status') }}:</text>
                <text class="info-value" :class="getStatusClass(rule.status)">{{
                  rule.status === 0 ? $t('firewall.notUsed') : rule.status === 1 && !rule.Port?.includes('-') ? $t('firewall.externalNetworkDisconnected') : $t('firewall.normal')
                }}</text>
              </view>
              <view class="rule-action">
                <text class="info-label">{{ $t('firewall.strategy') }}:</text>
                <text class="info-value" :class="rule.Strategy == 'accept' ? 'status-allow' : 'status-deny'">
                  {{ rule.Strategy == 'accept' ? $t('firewall.allow') : $t('firewall.block') }}
                </text>
              </view>
            </view>
            <view class="rule-note mt-10" v-if="rule.brief || portsPs[rule.Port]"
              >{{ $t('firewall.note') }}：{{ rule.brief || portsPs[rule.Port] || '' }}</view
            >
          </view>
        </view>
      </z-paging>

      <!-- 防火墙开启确认弹窗 -->
      <CustomDialog
        contentHeight="200rpx"
        v-model="showFirewallDialog"
        :title="$t('firewall.enableFirewall')"
        @confirm="confirmEnableFirewall"
        @cancel="cancelEnableFirewall"
      >
        <view class="text-secondary flex justify-center items-center h-full">
          {{ $t('firewall.enablePrompt') }}
        </view>
      </CustomDialog>

      <!-- 防火墙关闭确认弹窗 -->
      <CustomDialog
        contentHeight="200rpx"
        v-model="firewallDisabledPopup"
        :title="$t('firewall.disableFirewall')"
        @confirm="confirmCloseFirewall"
        @cancel="cancelCloseFirewall"
      >
        <view class="text-secondary flex justify-center items-center h-full">
          {{ $t('firewall.disablePrompt') }}
        </view>
      </CustomDialog>

      <!-- 删除确认对话框 -->
      <CustomDialog
        contentHeight="200rpx"
        v-model="showDeleteDialog"
        :title="$t('common.tip')"
        :confirmText="$t('common.delete')"
        :confirmStyle="{
          backgroundColor: '#FF3B30',
        }"
        @confirm="confirmDeleteRule"
      >
        <view class="text-secondary flex justify-center items-center h-full">
          {{ $t('firewall.deleteRuleConfirm', { port: currentRule?.Port + (currentRule?.Family === 'ipv6' ? $t('firewall.ipv6') : '') }) }}
        </view>
      </CustomDialog>
    </view>
  </page-container>
</template>

<script setup>
  import { onBackPress, onShow, onUnload } from '@dcloudio/uni-app';
  import { $t } from '@/locale/index.js';
  import { watch, ref } from 'vue';
  import {
    loading,
    firewallEnabled,
    currentRule,
    currentRuleIndex,
    portRules,
    rulePaging,
    pageContainer,
    showContextMenu,
    showDeleteDialog,
    menuPosition,
    clonePosition,
    getStatusClass,
    loadFirewallStatus,
    handleFirewallSwitch,
    confirmEnableFirewall,
    cancelEnableFirewall,
    reload,
    handleToggleRule,
    confirmDeleteRule,
    hideContextMenu,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleTouchCancel,
    showFirewallDialog,
    firewallDisabledPopup,
    cancelCloseFirewall,
    confirmCloseFirewall,
    portsPs,
    getPortRules
  } from './useController';
  import PageContainer from '@/components/PageContainer/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';

  const rulesList = ref([]);

  const virtualListChange = (vList) => {
    rulesList.value = vList;
  };

  /**
   * 查询规则列表
   */
  const queryRules = async (page, pageSize) => {
    try {
      if (!firewallEnabled.value) {
        rulePaging.value.complete([]);
        rulePaging.value.updateVirtualListRender();
        return;
      }
      const res = await getPortRules(page, pageSize);
      rulePaging.value.complete(res);
      rulePaging.value.updateVirtualListRender();
    } catch (error) {
      console.log(error);
      rulePaging.value.complete([]);
    }
  };

  // 监听加载状态
  watch(loading, (newVal) => {
    if (newVal) {
      uni.showLoading({
        title: $t('firewall.loading'),
      });
    } else {
      uni.hideLoading();
    }
  });

  // 初始化
  onShow(async () => {
    // 获取防火墙状态
    await loadFirewallStatus();
  });

  onBackPress(() => {
    // 如果长按菜单正在显示，先关闭菜单
    if (showContextMenu.value) {
      hideContextMenu();
      return true;
    }
    return false;
  });

  onUnload(() => {
    showFirewallDialog.value = false;
    firewallDisabledPopup.value = false;
    showDeleteDialog.value = false;
  });
</script>

<style lang="scss" scoped>
  .firewall-container {
    padding: 0;
    background-color: var(--bg-color);
  }

  /* 模糊遮罩 */
  .blur-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(4px);
    background-color: rgba(0, 0, 0, 0.15);
    z-index: 900;
    transition: all 0.2s ease;
  }

  /* 上下文菜单 */
  .context-menu {
    position: fixed;
    background-color: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.12);
    z-index: 999;

    // 水平位置类
    &.menu-left {
      transform: translateX(0);
    }

    &.menu-right {
      transform: translateX(0);
    }

    &.menu-center {
      transform: translateX(-50%);
    }

    &.menu-middle {
      margin-top: 0;
    }

    .menu-item {
      padding: 24rpx;
      display: flex;
      align-items: center;
      transition: all 0.2s ease;

      &:active {
        background-color: #f5f5f5;
        transform: scale(0.98);
      }

      .menu-text {
        margin-left: 16rpx;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      &.menu-delete {
        .menu-text-delete {
          color: #ff3b30;
        }
        &:active {
          background-color: rgba(255, 59, 48, 0.1);
        }
      }
    }

    .menu-divider {
      height: 1rpx;
      background-color: #f0f0f0;
      margin: 0 24rpx;
    }
  }

  /* 克隆容器 */
  .fixed-clone-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 901;
  }

  .rule-item-clone {
    padding: 30rpx;
    opacity: 0.9;
    border-radius: 14rpx;
  }

  /* 开关区域 */
  .switch-section {
    padding: 30rpx 40rpx;
    margin: 20rpx 20rpx 0;
    border-radius: 14rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);

    .switch-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  /* 规则项样式 */
  .rule-item-container {
    padding: 30rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;

    &:active {
      background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
      transform: scale(0.98);
    }

    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  .rule-main-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    padding-bottom: 12rpx;
    border-bottom: 1px solid var(--border-color);
  }

  .rule-protocol {
    font-size: 32rpx;
    font-weight: bold;
    color: var(--text-color-primary, #333);
  }

  .rule-port {
    font-size: 28rpx;
    color: var(--text-color-primary, #333);
  }

  .rule-extra-info {
    display: flex;
    justify-content: space-between;
    padding-bottom: 12rpx;
    margin-bottom: 12rpx;
    border-bottom: 1px solid var(--border-color);
  }

  .rule-status,
  .rule-action {
    display: flex;
    align-items: center;
  }

  .info-label {
    font-size: 26rpx;
    color: var(--text-color-tertiary, #999);
    margin-right: 8rpx;
  }

  .info-value {
    font-size: 26rpx;
    color: var(--text-color-secondary, #666);
    background-color: rgba(0, 0, 0, 0.03);
    padding: 4rpx 12rpx;
    border-radius: 20rpx;

    &.status-normal {
      color: #2ecc71;
      background-color: rgba(46, 204, 113, 0.1);
    }

    &.status-unused {
      color: #95a5a6;
      background-color: rgba(149, 165, 166, 0.1);
    }

    &.status-allow {
      color: #4cd964;
      background-color: rgba(76, 217, 100, 0.1);
    }

    &.status-deny {
      color: #ff9500;
      background-color: rgba(255, 149, 0, 0.1);
    }
  }

  .rule-note {
    font-size: 26rpx;
    color: var(--text-color-tertiary, #999);
    margin-top: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
