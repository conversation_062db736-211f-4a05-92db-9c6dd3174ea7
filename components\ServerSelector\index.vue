<template>
  <!-- 背景遮罩，点击时关闭下拉菜单 -->
  <view class="overlay" v-if="showDropdown" @click.stop="closeDropdown"></view>

  <view class="server-list">
    <view v-for="(item, index) in serverList" :key="item.ip" class="server-item">
      <view class="server-card" @click="handleSelect(item)" :class="{ 'server-card--active': item.status }">
        <view class="server-card__content">
          <view class="server-card__left">
            <view class="status-indicator" :class="{ 'status-indicator--online': item.status }">
              <view class="status-dot"></view>
              <text class="status-text">{{ item.status ? $t('server.online') : $t('server.offline') }}</text>
            </view>
            <view class="server-info">
              <text class="text-secondary server-name">{{ item.name }}</text>
              <text class="text-secondary server-ip">{{ item.ip }}</text>
            </view>
          </view>
          <view class="server-card__right">
            <view class="action-button" @click.stop="showActions(item, index)">
              <uv-icon name="more-dot-fill" size="24" color="var(--text-color-tertiary)"></uv-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 下拉菜单 -->
      <view class="dropdown-menu" v-if="currentServerIndex === index && showDropdown" @click.stop>
        <view class="dropdown-item text-primary" hover-class="dropdown-item-hover" @click="handleSelect(currentServer)">
          <uv-icon name="folder" size="18" color="#E6A23C"></uv-icon>
          <text>{{ $t('server.open') }}</text>
        </view>
        <view class="divider"></view>
        <view class="dropdown-item delete-item" hover-class="dropdown-item-hover" @click="handleDelete">
          <uv-icon name="trash-fill" size="18" color="#FF3B30"></uv-icon>
          <text>{{ $t('server.deleteServer') }}</text>
        </view>
      </view>
    </view>
  </view>

  <CustomDialog
    v-model="deleteModel"
    contentHeight="140rpx"
    :title="$t('common.deleteConfirm')"
    @close="deleteModel = false"
    @confirm="confirmDelete"
    :confirmStyle="{
      backgroundColor: '#FF3B30',
    }"
  >
    <view class="py-20 text-secondary">
      <text>{{ $t('server.deleteConfirmText', { ip: currentServer.ip }) }}</text>
    </view>
  </CustomDialog>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useServerListStore } from '@/pages/index/serverList/store';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import { $t } from '@/locale/index.js';

  const emits = defineEmits(['select', 'delete']);

  const { serverList } = useServerListStore().getReactiveState();
  const serverStore = useServerListStore();

  const showDropdown = ref(false);
  const currentServer = ref(null);
  const currentServerIndex = ref(null);
  const deleteModel = ref(false);

  const handleSelect = (item) => {
    emits('select', item);
    closeDropdown();
  };

  const showActions = (item, index) => {
    // 如果点击的是当前打开的菜单项，则关闭
    if (currentServerIndex.value === index && showDropdown.value) {
      showDropdown.value = false;
      return;
    }

    currentServer.value = item;
    currentServerIndex.value = index;
    showDropdown.value = true;
  };

  // 点击页面其他地方关闭下拉菜单
  const closeDropdown = () => {
    showDropdown.value = false;
  };

  const handleDelete = () => {
    if (currentServer.value) {
      deleteModel.value = true;
    }
  };

  const confirmDelete = async (close) => {
    closeDropdown();
    close();
    emits('delete', currentServer.value);
  };

  // 处理返回键关闭下拉菜单
  const handleBackPress = () => {
    if (showDropdown.value) {
      closeDropdown();
      return true; // 返回true表示已处理
    }
    return false;
  };

  onMounted(() => {
    // 添加返回键监听
    uni.addInterceptor('navigateBack', {
      success: handleBackPress,
    });
  });

  onUnmounted(() => {
    // 移除返回键监听
    uni.removeInterceptor('navigateBack');
  });
</script>

<style lang="scss" scoped>
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9;
    background-color: transparent;
  }

  .server-list {
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }

  .server-item {
    position: relative;
  }

  .server-card {
    background-color: var(--dialog-bg-color);
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    border: 2rpx solid transparent;
    
    &:active {
      transform: scale(0.98);
      background-color: var(--border-color);
    }
    
    &--active {
      border-color: var(--primary-color);
      box-shadow: 0 2rpx 16rpx rgba(0, 120, 255, 0.12);
    }
  }

  .server-card__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
  }

  .server-card__left {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-bottom: 8rpx;
    
    .status-dot {
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #ff3b30;
      position: relative;
      
      &:after {
        content: '';
        position: absolute;
        top: -4rpx;
        left: -4rpx;
        right: -4rpx;
        bottom: -4rpx;
        border-radius: 50%;
        background-color: rgba(255, 59, 48, 0.2);
        opacity: 0;
        animation: pulse 2s infinite;
      }
    }
    
    .status-text {
      font-size: 24rpx;
      color: #ff3b30;
    }
    
    &--online {
      .status-dot {
        background-color: var(--primary-color);
        
        &:after {
          background-color: rgba(0, 120, 255, 0.2);
        }
      }
      
      .status-text {
        color: var(--primary-color);
      }
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    70% {
      transform: scale(1.5);
      opacity: 0;
    }
    100% {
      transform: scale(0.8);
      opacity: 0;
    }
  }

  .server-info {
    display: flex;
    flex-direction: column;
  }

  .server-name {
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 4rpx;
  }

  .server-ip {
    font-size: 28rpx;
  }

  .action-button {
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    
    &:active {
      background-color: var(--border-color);
    }
  }

  /* 下拉菜单样式 */
  .dropdown-menu {
    position: absolute;
    right: 30rpx;
    top: 50%;
    z-index: 10;
    background-color: var(--dialog-bg-color);
    border-radius: 14rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
    padding: 8rpx 0;
    animation: slideIn 0.2s ease-out;
    transform-origin: top right;
    min-width: 180rpx;
    border: 1rpx solid  var(--border-color, rgba(0, 0, 0, 0.05));
  }

  @keyframes slideIn {
    from {
      transform: scale(0.9);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  .divider {
    height: 1rpx;
    background-color: var(--border-color, rgba(0, 0, 0, 0.05));
    margin: 2rpx 0;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    gap: 16rpx;

    &.delete-item {
      color: #ff3b30;
    }
    
    text {
      font-size: 28rpx;
    }
  }

  .dropdown-item-hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
</style>
