// import permission from '@/common/permission'
import { ref } from 'vue';
import Message from '@/hooks/useMessage';
import { useConfigStore } from '@/store';
import { $t } from '@/locale/index.js';
import { scanLogin } from '@/api/serverList';
import { requestCameraPermission } from '@/utils/common';
// Base64解码函数，兼容不同环境
const base64Decode = (str) => {
	try {
		// 浏览器环境
		if (typeof atob === 'function') {
			return atob(str);
		}
		// Node.js环境或其他环境
		return Buffer.from(str, 'base64').toString('binary');
	} catch (e) {
		// 鸿蒙环境专用解码方法
		// #ifdef APP-HARMONY
		try {
			// 将base64字符串转为ArrayBuffer
			const arrayBuffer = uni.base64ToArrayBuffer(str);
			// 将ArrayBuffer转为Uint8Array
			const uint8Array = new Uint8Array(arrayBuffer);
			// 将Uint8Array转为字符串
			return String.fromCharCode.apply(null, uint8Array);
		} catch (error) {
			console.error('鸿蒙环境Base64解码失败', error);
		}
		// #endif

		// 如果上述方法都失败，尝试使用uni-app提供的其他方法
		try {
			// 通用的uniapp解码方法
			const arrayBuffer = uni.base64ToArrayBuffer(str);
			const uint8Array = new Uint8Array(arrayBuffer);
			return Array.from(uint8Array)
				.map((byte) => String.fromCharCode(byte))
				.join('');
		} catch (error) {
			console.error('Base64解码失败', error);
			throw new Error('Base64解码失败');
		}
	}
};

export default function useScan(options = {}) {
	const { onScanSuccess, onScanError, onBindSuccess, onBindError, onLoginSuccess, onLoginError } = options;
	const { configList } = useConfigStore().getReactiveState();
	const isScanning = ref(false);
	// 添加定时器引用变量
	let pcConfirmTimer = null;
	let cloudConfirmTimer = null;
	// #ifdef APP-PLUS
	const mpaasScanModule = uni.requireNativePlugin('Mpaas-Scan-Module');
	// #endif

	// 扫码权限检查
	//   const checkScanPermission = () => {
	//     if (getApp().globalData.isPhone === 'ios') {
	//       if (!permission.judgeIosPermission('camera')) {
	//         return false
	//       }
	//     }
	//     return true
	//   }

	// 处理扫码结果
	const handleScanResult = async (result) => {
		const codeResult = result;
		// 未绑定用户
		if (codeResult === 'https://www.bt.cn/download/app.html') {
			onScanError && onScanError($t('scan.bindFail'));
			return;
		}

		// 云控绑定
		if (codeResult.substr(0, 6) === 'BTCOLL') {
			const cloud = codeResult.substr(6, codeResult.length);
			const cloudCode = base64Decode(cloud).split('|');
			await handleCloudBinding(cloudCode);
			return;
		}

		// 动态口令
		if (codeResult.indexOf('otpauth://totp/') !== -1) {
			uni.navigateTo({
				url: '/pages/checkAuth/checkAuth?code=' + encodeURI(codeResult),
			});
			return;
		}

		// 双因子认证
		const factor = base64Decode(codeResult).split('|');
		if (factor.length === 3) {
			uni.navigateTo({
				url: '/pages/checkAuth/checkAuth?code=' + encodeURI(codeResult),
			});
			return;
		}

		// 扫码登录
		if (codeResult.indexOf('tid=') !== -1) {
			await handleLogin(codeResult);
			return;
		}

		// 扫码绑定
		await handleBinding(codeResult);
	};

	// 处理绑定服务器
	const handleBinding = async (codeResult) => {
		try {
			const base64Result = base64Decode(codeResult).split('|');
			// 检查是否为内网地址
			if (base64Result[0].indexOf('127.0.0.1') !== -1) {
				onBindError && onBindError($t('server.internalNetworkError'));
				return;
			}

			// 检查是否已绑定
			const boundServer = configList.value.find((item) => item.panelPath === base64Result[0]);
			if (boundServer) {
				onBindError && onBindError($t('server.alreadyBound'));
				return;
			}

			// 构建绑定信息
			const bindInfo = {
				panelPath: base64Result[0],
				panelKey: base64Result[1],
				aesKey: base64Result[2],
				uuid: base64Result[3],
				system: base64Result[4] || 'linux',
			};

			// API秘钥检查
			if (bindInfo.panelKey.indexOf('*') !== -1) {
				onBindError && onBindError($t('server.apiKeyTooOld'));
				return;
			}

			// 获取最新的设备信息
			const deviceInfo = options.getDeviceInfo ? options.getDeviceInfo() : options.deviceInfo || {};

			// 发起绑定请求
			uni.request({
				url: `${bindInfo.panelPath}/check_bind`,
				data: {
					bind_token: bindInfo.uuid,
					client_brand: deviceInfo.brand,
					client_model: deviceInfo.model,
				},
				method: 'POST',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded',
				},
				sslVerify: false,
				success: (res) => {
					if (res.data === 0) {
						onBindError && onBindError($t('server.abnormalStatus'));
						return;
					}

					if (res.data !== 1) {
						onBindError && onBindError(res.data);
						return;
					}

					// 等待PC端确认
					waitPCConfirm(bindInfo)
						.then(() => {
							// 将新服务器添加到配置列表的首部（头部位置）
							configList.value.unshift(bindInfo);
							uni.setStorageSync('configList', configList.value);
							onBindSuccess && onBindSuccess(bindInfo);
						})
						.catch((error) => {
							onBindError && onBindError(error);
						});
				},
				fail: (error) => {
					onBindError && onBindError(error, bindInfo, deviceInfo);
				},
			});
		} catch (error) {
			onBindError && onBindError(error);
		}
	};

	// 等待PC端确认
	const waitPCConfirm = (bindInfo) => {
		return new Promise((resolve, reject) => {
			Message.loading($t('server.waitingForPC'));
			// 清除可能存在的定时器
			if (pcConfirmTimer) {
				clearInterval(pcConfirmTimer);
				pcConfirmTimer = null;
			}

			pcConfirmTimer = setInterval(() => {
				uni.request({
					url: `${bindInfo.panelPath}/get_app_bind_status`,
					data: { bind_token: bindInfo.uuid },
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded',
					},
					sslVerify: false,
					success: (res) => {
						if (res.data === 1) {
							clearInterval(pcConfirmTimer);
							pcConfirmTimer = null;
							Message.hideLoading();
							resolve();
						}
					},
					fail: (error) => {
						clearInterval(pcConfirmTimer);
						pcConfirmTimer = null;
						Message.hideLoading();
						reject(error);
					},
				});
			}, 1000);

			// 30秒超时
			setTimeout(() => {
				if (pcConfirmTimer) {
					clearInterval(pcConfirmTimer);
					pcConfirmTimer = null;
				}
				Message.hideLoading();
				reject(new Error($t('server.pcConfirmTimeout')));
			}, 30000);
		});
	};

	// 处理登录
	const handleLogin = async (codeResult) => {
		try {
			uni.showLoading({
				title: '正在登陆中...',
				mask: true,
			});

			const params = codeResult.split('&');
			const loginIndex = params[3].substr(params[3].indexOf('=') + 1, params[3].length) === 'windows' ? 4 : 3;

			const loginInfo = {
				panelPath: params[1].substr(params[1].indexOf('=') + 1, params[1].length),
				tid: params[loginIndex].substr(params[loginIndex].indexOf('=') + 1, params[loginIndex].length),
			};

			// 检查是否已绑定并获取服务器配置
			const boundServer = configList.value.find((item) => item.panelPath === loginInfo.panelPath);

			if (!boundServer) {
				uni.hideLoading();
				throw new Error($t('server.bindBeforeLogin'));
			}

			// 设置当前服务器配置（关键步骤：设置全局配置以便API请求到正确的服务器）
			const { PATH, UUID, SECRET, AESKEY, SYSTEM } = await import('@/utils/config');
			PATH.value = boundServer.panelPath;
			UUID.value = boundServer.uuid;
			SECRET.value = boundServer.panelKey;
			AESKEY.value = boundServer.aesKey;
			SYSTEM.value = boundServer.system || 'linux';

			// 发起登录请求
			const res = await scanLogin({ tid: loginInfo.tid });

			if (!res.status) {
				uni.hideLoading();
				onLoginError && onLoginError(new Error(res.msg));
				return;
			}

			// 成功后延迟隐藏loading（保持与原代码一致）
			setTimeout(() => {
				uni.hideLoading();
				onLoginSuccess && onLoginSuccess(loginInfo);
			}, 1800);
		} catch (error) {
			uni.hideLoading();
			onLoginError && onLoginError(error);
		}
	};

	// 处理云控绑定
	const handleCloudBinding = (cloudCode) => {
		uni.request({
			url: `${cloudCode[0]}/api?action=check_bind`,
			data: {
				request_token: cloudCode[1],
				bind_token: cloudCode[2],
				client_brand: options.deviceInfo?.brand,
				client_model: options.deviceInfo?.model,
			},
			method: 'POST',
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
			sslVerify: false,
			success: (res) => {
				if (res.data !== 1) {
					onBindError && onBindError(new Error($t('server.cloudBindFailed')));
					return;
				}

				// 等待云控PC端确认
				waitCloudConfirm(cloudCode)
					.then((data) => {
						onBindSuccess &&
							onBindSuccess({
								type: 'cloud',
								cloudCode,
							});
					})
					.catch((error) => {
						onBindError && onBindError(error);
					});
			},
			fail: (error) => {
				onBindError && onBindError(error);
			},
		});
	};

	// 等待云控PC端确认
	const waitCloudConfirm = (cloudCode) => {
		return new Promise((resolve, reject) => {
			// 显示loading提示
			Message.loading($t('server.waitingForPC'));
			uni.showLoading({
				title: $t('server.waitingForPC'),
				mask: true,
			});

			// 清除可能存在的定时器
			if (cloudConfirmTimer) {
				clearInterval(cloudConfirmTimer);
				cloudConfirmTimer = null;
			}

			cloudConfirmTimer = setInterval(() => {
				uni.request({
					url: `${cloudCode[0]}/api?action=get_app_bind_status`,
					data: {
						request_token: cloudCode[1],
						bind_token: cloudCode[2],
					},
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded',
					},
					sslVerify: false,
					success: (res) => {
						if (res.data.status === 1) {
							clearInterval(cloudConfirmTimer);
							cloudConfirmTimer = null;
							Message.hideLoading(); // 隐藏loading
							resolve(res.data);
						}
					},
					fail: (error) => {
						clearInterval(cloudConfirmTimer);
						cloudConfirmTimer = null;
						Message.hideLoading(); // 隐藏loading
						reject(error);
					},
				});
			}, 1000);

			setTimeout(() => {
				if (cloudConfirmTimer) {
					clearInterval(cloudConfirmTimer);
					cloudConfirmTimer = null;
				}
				Message.hideLoading(); // 超时时也要隐藏loading
				reject(new Error($t('server.cloudConfirmTimeout')));
			}, 30000);
		});
	};

	// 清除所有定时器的函数
	const clearAllTimers = () => {
		if (pcConfirmTimer) {
			clearInterval(pcConfirmTimer);
			pcConfirmTimer = null;
		}
		if (cloudConfirmTimer) {
			clearInterval(cloudConfirmTimer);
			cloudConfirmTimer = null;
		}
		// 确保隐藏所有可能的loading
		Message.hideLoading();
		uni.hideLoading();
	};

	// 开始扫码
	const startScan = async () => {
		// #ifdef APP-PLUS
		try {
			await requestCameraPermission();
		} catch (error) {
			onScanError && onScanError(error);
			return;
		}
		// #endif
		// 清除所有可能存在的定时器
		clearAllTimers();

		if (isScanning.value) {
			isScanning.value = false; // 如果已经在扫描中，重置状态
			return;
		}

		// if (!checkScanPermission()) {
		//   onScanError && onScanError('没有相机权限')
		//   return
		// }
		isScanning.value = true;
		// #ifdef APP-PLUS
		mpaasScanModule.mpaasScan(
			{
				// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
				scanType: ['qrCode', 'barCode'],
				// 是否隐藏相册，默认false不隐藏
				hideAlbum: false,
				//ios需要设置这个参数，只支持中英文 zh-Hans、en，默认中文
				language: 'zh-Hans',
				//相册选择照片识别错误提示(ios)
				failedMsg: $t('checkAuth.unrecognizedQRCode'),
				//Android支持全屏需要设置此参数
				screenType: 'full',
				timeoutInterval: '10', //设置超时时间
				timeoutText: $t('checkAuth.timeoutText'), //超时提醒文本
			},
			(ret) => {
				handleScanResult(ret.resp_result);
				onScanSuccess && onScanSuccess(ret);
				isScanning.value = false;
			},
		);
		// uni.scanCode({
		//   onlyFromCamera: true,
		//   scanType: ['qrCode'],
		//   success: (res) => {
		//     handleScanResult(res.result);
		//     onScanSuccess && onScanSuccess(res)
		//   },
		//   complete: () => {
		//     isScanning.value = false;
		//   },
		// });
		// #endif

		// #ifdef APP-HARMONY
		uni.scanCode({
			onlyFromCamera: true,
			scanType: ['qrCode'],
			success: (res) => {
				handleScanResult(res.result);
			},
			complete: () => {
				isScanning.value = false;
			},
		});
		// #endif
	};

	return {
		isScanning,
		startScan,
		clearAllTimers, // 导出清理函数，以便在组件卸载时调用
	};
}
