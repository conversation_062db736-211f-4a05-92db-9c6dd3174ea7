import { ref, computed } from 'vue'
import { $i18n } from '../locale/index.js'

export function useLanguage() {
  // 当前语言
  const currentLocale = ref($i18n.locale)
  
  // 可用的语言列表
  const availableLocales = computed(() => {
    return [
      { code: 'zh-CN', name: '中文' },
      { code: 'en-US', name: 'English' }
    ]
  })
  
  // 切换语言
  const changeLocale = (locale) => {
    if (locale === currentLocale.value) return
    
    // 更新当前语言
    currentLocale.value = locale
    $i18n.locale.value = locale
    
    // 将语言保存到本地存储
    uni.setStorageSync('language', locale)
    
    // 可选：重启应用以应用新语言
    // uni.reLaunch({
    //   url: '/pages/index/index'
    // })
  }

  return {
    currentLocale,
    availableLocales,
    changeLocale
  }
} 