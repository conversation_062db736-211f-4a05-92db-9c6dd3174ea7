<template>
	<page-container :title="installInfo.title || '安装插件'">
		<!-- 步骤指示器 -->
		<view class="stepper">
			<view
				v-for="(step, index) in steps"
				:key="index"
				class="step"
				:class="{ active: currentStep === index, completed: currentStep > index }"
			>
				<view class="step-number">
					<text v-if="currentStep <= index">{{ index + 1 }}</text>
					<text v-else class="check-icon">✓</text>
				</view>
				<text class="step-title text-center">{{ step }}</text>
				<view v-if="index < steps.length - 1" class="step-line"></view>
			</view>
		</view>

		<!-- 步骤内容区域 -->
		<view class="step-content">
			<!-- 第一步 选择版本和安装的类型 -->
			<view v-if="currentStep === 0" class="step-panel">
				<view class="version-selector">
					<text class="section-title">{{ $t('linux.install.selectVersion') }}</text>
					<view class="cards-container w-full">
						<scroll-view scroll-y class="w-full h-30vh">
							<view
								v-for="version in versions"
								:key="version.id"
								class="card mb-10 mx-10"
								:class="{ active: selectedVersion === version.m_version }"
								@click="selectedVersion = version.m_version"
							>
								<view class="card-content">
									<text class="card-title text-primary">{{
										`${version.m_version}.${version.version}`
									}}</text>
								</view>
								<view class="card-check" v-if="selectedVersion === version.m_version">
									<text class="check-icon">✓</text>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>

				<view class="actions">
					<BtButton type="success" class="!w-full" :loading="loading" @tap="nextStep">
						{{ $t('linux.install.speedInstall') }}
					</BtButton>
				</view>
			</view>

			<!-- 第二步 显示安装日志 -->
			<view v-else-if="currentStep === 1" class="step-panel">
				<view class="install-progress">
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: `${installProgress}%` }"></view>
					</view>
					<text class="progress-text">{{ installProgress }}%</text>
				</view>

				<view class="log-container">
					<view class="log-header">
						<text class="log-title text-primary">{{ $t('linux.install.installLog') }}</text>
						<text class="log-status" :class="{ 'status-error': hasError }">
							{{ hasError ? $t('linux.install.error') : $t('linux.install.installing') }}
						</text>
					</view>
					<scroll-view class="log-content" scroll-y :scroll-into-view="scrollToId">
						<text
							v-for="(log, index) in installLogs"
							:key="index"
							:id="`log-${index}`"
							:class="{ 'log-error': log.type === 'error' }"
						>
							{{ log.timestamp }} - {{ log.message }}
						</text>
					</scroll-view>
				</view>

				<view class="actions">
					<BtButton
						type="default"
						class="!w-full"
						:hairline="true"
						:customStyle="{ border: '1px solid #ccc', color: 'var(--text-color-primary)' }"
						:disabled="loading"
						@tap="prevStep"
						>{{ $t('linux.install.previous') }}</BtButton
					>
					<BtButton
						type="success"
						class="!w-full"
						:loading="installProgress < 100 && !hasError"
						@tap="nextStep"
					>
						{{ hasError ? $t('linux.install.retry') : $t('linux.install.complete') }}
					</BtButton>
				</view>
			</view>

			<!-- 第三步 安装完成返回上一级界面 -->
			<view v-else class="step-panel">
				<view class="completion-card">
					<view class="completion-icon">
						<text class="success-icon">✓</text>
					</view>
					<text class="completion-title">{{ $t('linux.install.installSuccess') }}</text>
					<text class="completion-message">
						{{ installInfo.title }} {{ $t('linux.install.successMessage') }}
					</text>
					<view class="completion-details">
						<view class="detail-item">
							<text class="detail-label">{{ $t('linux.install.details.version') }}</text>
							<text class="detail-value text-secondary">{{
								`${getVersionName().m_version}.${getVersionName().version}`
							}}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">{{ $t('linux.install.details.status') }}</text>
							<text class="detail-value status-running">{{ $t('linux.install.details.running') }}</text>
						</view>
					</view>
				</view>

				<view class="actions">
					<BtButton type="success" class="!w-full" @tap="goBack">{{ $t('linux.install.return') }}</BtButton>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onLoad } from '@dcloudio/uni-app';
	import { ref, computed, onMounted, onUnmounted } from 'vue';
	import { getPluginInfo, installPlugin, getPluginLog, inputPackageInfo } from '@/api/config';
	import BtButton from '@/components/BtButton/index.vue';
	import { $t } from '@/locale';

	const installInfo = ref({});
	const currentStep = ref(0);
	const steps = [
		$t('linux.install.selectVersion'),
		$t('linux.install.installProcess'),
		$t('linux.install.completeInstallation'),
	];

	const scrollToId = computed(() => {
		return installLogs.value.length > 0 ? `log-${installLogs.value.length - 1}` : '';
	});

	// 版本选择数据
	const selectedVersion = ref('');
	const versions = ref([]);

	// 安装选项
	const selectedOptions = ref([]);

	// 安装日志和进度
	const installProgress = ref(0);
	const installLogs = ref([]);
	const hasError = ref(false);
	const autoScroll = ref(true);

	// 模拟安装进度
	let installInterval = null;

	const loading = ref(false); // 按钮加载状态

	onLoad(async (option) => {
		installInfo.value.name = option.name;
	});

	// 切换安装选项
	const toggleOption = (optionId) => {
		if (selectedOptions.value.includes(optionId)) {
			selectedOptions.value = selectedOptions.value.filter((id) => id !== optionId);
		} else {
			selectedOptions.value.push(optionId);
		}
	};

	// 下一步
	const nextStep = () => {
		if (currentStep.value === 0) {
			startInstallation();
		} else if (currentStep.value === 1 && hasError.value) {
			// 重试安装
			hasError.value = false;
			installLogs.value = [];
			installProgress.value = 0;
			startInstallation();
			return;
		}

		if (currentStep.value < steps.length - 1) {
			currentStep.value++;
		}
	};

	// 上一步
	const prevStep = () => {
		if (currentStep.value > 0) {
			if (currentStep.value === 1) {
				// 如果从安装页面返回，清除安装进程
				if (installInterval) {
					clearInterval(installInterval);
					installInterval = null;
				}
				installLogs.value = [];
				installProgress.value = 0;
				hasError.value = false;
			}
			currentStep.value--;
		}
	};

	// 开始安装
	const startInstallation = async () => {
		try {
			loading.value = true;
			// 添加初始日志
			addInstallLog($t('linux.install.logs.startInstall') + ' ' + installInfo.value.title);
			addInstallLog($t('linux.install.logs.preparing'));

			let params = getVersionName();
			const res = await installPlugin({
				sName: installInfo.value.name,
				min_version: params.version,
				version: params.m_version,
				type: 1,
			});
			if (res.name === 'monitor' || res.name === 'btwaf') {
				installMonitorReport(res);
				return;
			}
			if (res.status) {
				// 开始轮询日志
				startPollingLogs();
			} else {
				hasError.value = true;
				addInstallLog(res.msg || $t('linux.install.logs.installFailed'), 'error');
				loading.value = false;
			}
		} catch (error) {
			hasError.value = true;
			addInstallLog($t('linux.install.logs.error') + ' ' + error.message, 'error');
			loading.value = false;
		}
	};

	// 监控报表安装
	const installMonitorReport = async (data) => {
		try {
			if (installInterval) {
				clearInterval(installInterval);
			}
			installInterval = setInterval(() => {
				if (installProgress.value === 99) {
					return;
				}
				addInstallLog(`正在安装${installInfo.value.title}`);
				installProgress.value += 1;
			}, 1000);
			const res = await inputPackageInfo({
				plugin_name: data.name,
				tmp_path: data.tmp_path,
				install_opt: data.install_opt,
			});
			if (res.status) {
				installProgress.value = 100;
				addInstallLog($t('linux.install.logs.installComplete'));
				stopPollingLogs();
				nextStep();
			}
		} catch (error) {
			console.error('获取日志失败:', error);
		}
	};

	// 轮询获取日志
	const startPollingLogs = () => {
		if (installInterval) {
			clearInterval(installInterval);
		}

		let estimateProgress = 0;
		const progressInterval = 15; // 每次增加的进度值
		const maxProgress = 99; // 最大进度限制

		installInterval = setInterval(async () => {
			try {
				const logRes = await getPluginLog();
				if (logRes) {
					// 估算进度
					if (estimateProgress < maxProgress) {
						estimateProgress += progressInterval;
						if (estimateProgress > maxProgress) {
							estimateProgress = maxProgress;
						}
						installProgress.value = estimateProgress;
					}

					// 添加新日志
					if (logRes.title) {
						addInstallLog(logRes.title);
					}

					// 检查是否完成或失败
					if (logRes.status === false) {
						stopPollingLogs();
						installProgress.value = 100;
						addInstallLog($t('linux.install.logs.installComplete'));
						loading.value = false;
						nextStep();
					}
				}
			} catch (error) {
				console.error('获取日志失败:', error);
			}
		}, 2000); // 每2秒轮询一次
	};

	// 停止轮询
	const stopPollingLogs = () => {
		if (installInterval) {
			clearInterval(installInterval);
			installInterval = null;
			loading.value = false;
		}
	};

	// 组件卸载时清理定时器
	onUnmounted(() => {
		stopPollingLogs();
	});

	// 添加安装日志
	const addInstallLog = (message, type = 'info') => {
		const now = new Date();
		const timestamp = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(
			2,
			'0',
		)}:${String(now.getSeconds()).padStart(2, '0')}`;
		installLogs.value.push({ timestamp, message, type });
	};

	// 获取版本名称
	const getVersionName = () => {
		const version = versions.value.find((v) => v.m_version === selectedVersion.value);
		return version ? version : '';
	};

	const getVersion = async () => {
		try {
			const res = await getPluginInfo({
				sName: installInfo.value.name,
			});
			versions.value = res.versions;
			installInfo.value.title = res.title;
			if (versions.value.length > 0) {
				selectedVersion.value = versions.value[0].m_version;
			}
		} catch (e) {
			console.log(e);
		}
	};

	// 返回上一级
	const goBack = () => {
		uni.navigateBack({
			complete: () => {
				if (installInfo.value.name === 'monitor') {
					setTimeout(() => {
						uni.redirectTo({
							url: '/linux/monitorReport/index',
						});
					}, 200);
				}
			},
		});
	};

	onMounted(async () => {
		await getVersion();
	});
</script>

<style lang="scss" scoped>
	.stepper {
		display: flex;
		justify-content: space-between;
		position: relative;
		padding: 20rpx 0;

		.step {
			display: flex;
			flex-direction: column;
			align-items: center;
			position: relative;
			flex: 1;

			.step-number {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				background-color: #f0f0f0;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 16rpx;
				font-weight: 500;
				color: #666;
				transition: all 0.3s;
			}

			.step-title {
				font-size: 28rpx;
				color: #666;
				transition: all 0.3s;
			}

			.step-line {
				position: absolute;
				top: 30rpx;
				right: calc(-50% + 30rpx);
				width: 100%;
				height: 2px;
				background-color: #f0f0f0;
				z-index: -1;
			}

			&.active {
				.step-number {
					background-color: var(--primary-color);
					color: white;
				}

				.step-title {
					color: var(--primary-color);
					font-weight: 500;
				}
			}

			&.completed {
				.step-number {
					background-color: var(--primary-color);
					color: white;
				}

				.step-line {
					background-color: var(--primary-color);
				}
			}
		}
	}

	.step-content {
		padding: 0 30rpx;
		height: 80vh;
	}

	.step-panel {
		background-color: var(--dialog-bg-color);
		border-radius: 24rpx;
		padding: 40rpx;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
		margin-bottom: 40rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		margin-bottom: 30rpx;
		display: block;
		color: var(--text-color-primary);
	}

	.cards-container {
		display: inline-flex;
		flex-direction: column;
		gap: 24rpx;
		margin-bottom: 40rpx;
	}

	.card {
		padding: 32rpx;
		border-radius: 16rpx;
		background-color: var(--dialog-bg-color);
		display: flex;
		align-items: center;
		cursor: pointer;
		transition: all 0.2s;
		border: 2px solid transparent;

		&:first-child {
			margin-top: 20rpx;
		}

		&.active {
			border-color: var(--primary-color);
			background-color: rgba(var(--primary-color-rgb), 0.05);
		}

		.card-content {
			flex: 1;
		}

		.card-title {
			font-weight: 600;
			font-size: 30rpx;
			display: block;
		}

		.card-check {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
			background-color: var(--primary-color);
			display: flex;
			align-items: center;
			justify-content: center;

			.check-icon {
				color: white;
				font-size: 28rpx;
			}
		}
	}

	.option-list {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		margin-bottom: 40rpx;
	}

	.option-item {
		display: flex;
		align-items: flex-start;
		padding: 24rpx;
		background-color: #f9f9f9;
		border-radius: 16rpx;

		checkbox {
			margin-right: 20rpx;
			margin-top: 4rpx;
		}

		.option-info {
			flex: 1;
		}

		.option-name {
			font-weight: 500;
			font-size: 28rpx;
			display: block;
			margin-bottom: 4rpx;
		}

		.option-desc {
			font-size: 24rpx;
			color: #666;
		}
	}

	.actions {
		display: flex;
		justify-content: space-between;
		gap: 24rpx;

		button {
			flex: 1;
			padding: 12rpx 0;
			border-radius: 12rpx;
			font-size: 30rpx;
			border: none;

			&.btn-primary {
				background-color: var(--primary-color);
				color: white;

				&:disabled {
					background-color: var(--bg-color-secondary);
					color: var(--text-color-secondary);
				}
			}

			&.btn-secondary {
				background-color: var(--bg-color-secondary);
				color: var(--text-color-secondary);
			}
		}
	}

	.install-progress {
		margin-bottom: 30rpx;

		.progress-bar {
			height: 16rpx;
			background-color: #f0f0f0;
			border-radius: 8rpx;
			overflow: hidden;
			margin-bottom: 10rpx;

			.progress-fill {
				height: 100%;
				background-color: var(--primary-color);
				transition: width 0.3s;
			}
		}

		.progress-text {
			font-size: 26rpx;
			color: #666;
			text-align: right;
		}
	}

	.log-container {
		background-color: var(--border-color);
		border-radius: 16rpx;
		margin-bottom: 40rpx;
		max-height: 600rpx;
		display: flex;
		flex-direction: column;

		.log-header {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 24rpx;
			border-bottom: 1px solid #e0e0e0;

			.log-title {
				font-weight: 500;
				font-size: 28rpx;
			}

			.log-status {
				font-size: 26rpx;
				color: #4caf50;

				&.status-error {
					color: #f44336;
				}
			}
		}

		.log-content {
			padding: 20rpx 24rpx;
			font-size: 24rpx;
			color: var(--text-color-secondary);
			flex: 1;
			max-height: 500rpx;

			text {
				display: block;
				line-height: 1.5;
				white-space: pre-wrap;

				&.log-error {
					color: #f44336;
				}
			}
		}
	}

	.completion-card {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx;

		.completion-icon {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
			background-color: #4caf50;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 30rpx;

			.success-icon {
				color: white;
				font-size: 80rpx;
			}
		}

		.completion-title {
			font-size: 40rpx;
			font-weight: 600;
			margin-bottom: 16rpx;
			color: var(--text-color-primary);
		}

		.completion-message {
			font-size: 28rpx;
			color: #666;
			text-align: center;
			margin-bottom: 40rpx;
		}

		.completion-details {
			width: 100%;
			border-top: 1px solid #eee;
			padding-top: 30rpx;

			.detail-item {
				display: flex;
				margin-bottom: 20rpx;

				.detail-label {
					width: 180rpx;
					font-size: 28rpx;
					color: #666;
				}

				.detail-value {
					flex: 1;
					font-size: 28rpx;
					font-weight: 500;

					&.status-running {
						color: #4caf50;
					}
				}
			}
		}
	}
</style>
