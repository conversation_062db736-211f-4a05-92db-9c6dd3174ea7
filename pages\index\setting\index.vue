<template>
  <page-container ref="pageContainerRef" :title="$t('setting.settings')" :is-back="false" :has-tab-bar="true">
    <view class="setting-container">
      <!-- 遍历设置分组 -->
      <view v-for="(group, groupIndex) in settingGroups" :key="groupIndex" class="setting-group">
        <!-- 分组标题 -->
        <view class="group-title">{{ group.title }}</view>

        <!-- 分组内容 -->
        <uv-cell-group :border="false" class="cell-group border border-gray-200">
          <template v-for="(item, index) in group.items" :key="index">
            <uv-cell
              :is-link="item.isLink"
              :customStyle="customStyle"
              :arrow="item.arrow"
              @click="item.onClick && item.onClick()"
              :clickable="true"
              :border="false"
              class="cell-item py-10 border-bottom border-gray-200"
            >
              <template #icon v-if="item.icon">
                <uni-icons
                  v-if="item.iconType === 'uni'"
                  size="18"
                  :type="item.icon"
                  color="var(--text-color-secondary)"
                />
                <uv-icon v-else :name="item.icon" color="var(--text-color-secondary)" />
              </template>
              <template #title v-if="item.title">
                <text class="text-primary text-32">{{ item.title }}</text>
              </template>
              <template #right-icon v-if="item.slotComponent">
                <component
                  :is="item.slotComponent.component"
                  v-bind="item.slotComponent.props || {}"
                  v-on="item.slotComponent.events || {}"
                />
              </template>
              <template #value v-if="item.value">
                <text class="text-secondary text-28">{{ item.value }}</text>
              </template>
              <template #label v-if="item.label">
                <text class="text-secondary text-28">{{ item.label }}</text>
              </template>
            </uv-cell>
          </template>
        </uv-cell-group>
      </view>
    </view>
    <CustomDialog
      v-model="unlockModel"
      contentHeight="120rpx"
      :title="$t('setting.closeConfirm')"
      @close="unlockModel = false"
      @confirm="confirmCloseUnlock"
    >
      <view class="h-full flex items-center justify-center">
        <text class="text-secondary">{{ $t('setting.confirmCloseUnlock') }}</text>
      </view>
    </CustomDialog>
  </page-container>
</template>

<script setup>
  import PageContainer from '@/components/PageContainer/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import { settingGroups, pageContainer, confirmCloseUnlock, unlockModel } from './useMethods';
  import { ref, onMounted } from 'vue';
  import { $t } from '@/locale/index.js';

  const customStyle = {
    backgroundColor: 'var(--bg-color)',
    color: 'var(--bg-color-secondary)',
  };

  const pageContainerRef = ref(null);

  onMounted(() => {
    // 将本地ref赋值给全局pageContainer
    if (pageContainerRef.value) {
      pageContainer.value = pageContainerRef.value;
    }
  });
</script>

<style lang="scss" scoped>
  .setting-container {
    padding: 10rpx 0;
  }

  .setting-group {
    margin-bottom: 30rpx;
  }

  .group-title {
    padding: 20rpx 30rpx;
    font-size: 30rpx;
    color: var(--text-color-secondary);
    font-weight: 500;
  }

  .cell-group {
    border-radius: 16rpx;
    overflow: hidden;
    margin: 0 20rpx;
  }

  .cell-item {
    transition: all 0.2s ease;
    &:active {
      background-color: #f9f9f9;
      transform: scale(0.98);
    }
  }
</style>
