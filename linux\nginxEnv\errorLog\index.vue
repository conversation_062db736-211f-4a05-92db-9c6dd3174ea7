<template>
	<page-container ref="pageContainer" :is-back="true" title="Nginx错误日志">
		<view class="log-container">
			<!-- 操作栏 -->
			<view class="log-actions mt-20">
				<view class="action-btn" @tap="refreshLogs" hover-class="action-hover">
					<uv-icon name="reload" size="20" color="#20a50a"></uv-icon>
					<text class="action-text">刷新</text>
				</view>
			</view>

			<!-- 日志内容 -->
			<view class="log-content">
				<scroll-view
					class="log-display"
					scroll-y="true"
					:scroll-top="scrollTop"
					ref="scrollView"
				>
					<text class="log-text text-white">{{ logContent }}</text>
				</scroll-view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
import PageContainer from '@/components/PageContainer/index.vue';
import { onShow } from '@dcloudio/uni-app';
import {
	pageContainer,
	logContent,
	scrollTop,
	refreshLogs,
	initErrorLogData
} from './useController.js';

onShow(async () => {
	await initErrorLogData();
});
</script>

<style lang="scss" scoped>
.log-container {
	padding: 0 20rpx;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	height: 100%;
	background-color: #f5f5f5;
}

/* 操作栏 */
.log-actions {
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 15rpx 20rpx;
	display: flex;
	justify-content: flex-start;
}

.action-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 12rpx 20rpx;
	border-radius: 4px;
	background-color: #fff;
	border: 1px solid #ddd;
	transition: all 0.2s ease;
	gap: 8rpx;
	cursor: pointer;

	.action-text {
		font-size: 24rpx;
		color: #333;
		font-weight: normal;
	}
}

.action-hover {
	background-color: #f0f0f0;
	border-color: #20a50a;
}

/* 日志内容 */
.log-content {
	flex: 1;
	background-color: #000000;
	border: 1px solid #ddd;
	border-radius: 4px;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.log-display {
	height: 0;
	flex: 1;
	background-color: #000;
	font-family: 'Courier New', monospace;
}

.log-text {
	font-size: 22rpx;
	line-height: 1.4;
	white-space: pre-wrap;
	word-break: break-all;
	display: block;
	padding: 20rpx;
}
</style>
