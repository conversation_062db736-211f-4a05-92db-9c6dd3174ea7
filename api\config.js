import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 查找指定插件信息
 * @param { string } data.sName 插件名称
 * @returns { Promise } 返回值
 */
export const getPluginInfo = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/plugin?action=get_soft_find' : '/v2/plugin?action=get_soft_find';
	data['is_verify'] = false;
	return axios(url, data);
};

/**
 * @description 安装插件
 * @param { string } data.sName 插件名称
 * @param { string } data.min_version
 * @param { string } data.version
 * @param { string } data.type 插件类型
 * @returns { Promise } 返回值
 */
export const installPlugin = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/plugin?action=install_plugin' : '/v2/plugin?action=install_plugin';
	data['is_verify'] = false;
	return axios(url, data, 'POST', (res) => {
		if (DEFAULT_API_TYPE === 'domestic') {
			return res;
		} else {
			return {
				status: res?.result ? true : false,
				msg: res?.result,
			};
		}
	});
};

/**
 * @description 获取插件安装日志
 * @returns { Promise } 返回值
 */
export const getPluginLog = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/panel/msgbox/get_installed_msg' : '/v2/files?action=GetTaskSpeed';
	let data = {};
	data['is_verify'] = false;
	return axios(url, data, 'POST', (res) => {
		if (DEFAULT_API_TYPE === 'domestic') {
			return res;
		} else {
			return {
				title: res?.name,
				status: res?.result ? false : true,
			};
		}
	});
};

/**
 * @description 获取插件服务状态
 * @param { string } data.name 服务名称
 * @returns { Promise } 返回值
 */
export const getSoftStatus = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic' ? '/panel/public/get_soft_status' : '/v2/panel/public/get_public_config';
	data['is_verify'] = false;
	return axios(url, data, 'POST', (res) => {
		if (DEFAULT_API_TYPE === 'domestic') {
			return res;
		} else {
			const serverData = res[data.name] || {};
			return {
				...serverData,
				s_status: serverData?.setup,
			};
		}
	});
};

/**
 * @description 获取插件版本信息
 * @param { string } data.plugin_name 插件名称
 * @param { string } data.tmp_path 临时路径
 * @param { string } data.install_opt 安装选项
 * @returns { Promise } 返回值
 */
export const inputPackageInfo = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/plugin?action=input_package' : '/v2/plugin?action=input_package';
	return axios(url, data, 'POST', (res) => {
		return res;
	});
};

/**
 * @description 获取公共配置信息
 * @returns { Promise } 返回值
 */
export const getPublicConfig = () => {
	return axios('/panel/public/get_public_config');
};

/**
 * @description 设置面板配置
 * @param { string } data.webname 面板别名
 * @param { number } data.session_timeout 超时时间
 * @param { string } data.domain 绑定域名
 * @param { string } data.limitip 授权IP
 * @param { string } data.sites_path 默认建站目录
 * @param { string } data.backup_path 默认备份目录
 * @param { string } data.address 服务器IP
 * @param { string } data.systemdate 服务器时间
 * @returns { Promise }
 */
export const setPanelConfig = (data) => {
	console.log('data:' + JSON.stringify(data));
	return axios('/config?action=setPanel', data);
};

/**
 * @description 开发者模式
 * @returns { Promise }
 */
export const setDebug = () => {
	return axios('/config?action=set_debug');
};

/**
 * @description 服务管理
 * @param { string } data.name 服务名称
 * @param { string } data.type 操作类型 start|stop|restart|reload
 * @returns { Promise }
 */
export const serviceAdmin = (data) => {
	return axios('/system?action=ServiceAdmin', data, 'POST');
};

/**
 * @description 重启服务器
 * @returns { Promise }
 */
export const restartServer = () => {
	return axios('/system?action=RestartServer', {}, 'POST');
};
