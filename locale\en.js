export default {
	common: {
		cancel: 'Cancel',
		submit: 'Submit',
		loading: 'Loading in progress ..',
		success: 'Success',
		fail: 'Fail',
		language: 'Language',
		delete: 'Delete',
		deleteConfirm: 'Do want to delete it?',
		deleteSuccess: 'Delete successfully',
		deleteFail: 'Delete failed',
		refresh: 'Refresh',
		refreshSuccess: 'Refresh successful',
		refreshFail: 'Refresh failed',
		rename: 'Rename',
		modify: 'Modify',
		tip: 'Tips',
		confirm: 'Confirm',
	},
	home: {
		welcome: 'Welcome to use aaPanel',
		dashboard: 'Dashboard',
		settings: 'Setup',
		fileManagement: 'File',
	},
	server: {
		serverList: 'Server list',
		dynamicAuth: 'One-Time Password',
		servers: 'Server list',
		memory: 'Memory',
		cpu: 'CPU',
		disk: 'Hard disk',
		network: 'Network',
		serverConnectionFailed: 'Server connection failed!',
		load: 'Load',
		smooth: 'Smooth',
		connecting: 'Connected ..',
		deleteConfirmText: 'Do you want to delete the server with IP address [{ip}]?',
		btPanel: 'aaPanel',
		online: 'Online',
		offline: 'Offline',
		open: 'Open',
		deleteServer: 'Delete Server',
		day: '{days} days',
		internalNetworkError: 'Please use an external network address to access the panel and rebind it!',
		alreadyBound: 'This server is already bound!',
		apiKeyTooOld: 'The API key is too old, please reset the API key before binding!',
		sort: 'Sort',
		sortServers: 'Sort Servers',
		sortTip: 'Long press and drag to adjust server order',
		sortComplete: 'Sort order saved',
		abnormalStatus:
			'The information status is abnormal. Please delete the browser cache, refresh the QR code, and then scan the code again to bind!',
		waitingForPC: 'Waiting for PC confirmation',
		pcConfirmTimeout: 'Waiting for PC confirmation timeout',
		bindBeforeLogin: 'Please bind the server first, and then scan the code to log in!',
		cloudBindFailed: 'Cloud control binding failed',
		cloudConfirmTimeout: 'Waiting for cloud control PC to confirm timeout',
		iosPrompt: 'View tutorial',
	},
	setting: {
		settings: 'Settings',
		theme: 'Theme',
		checkUpdate: 'Check for updates',
		dark: 'Dark',
		light: 'Light',
		currentVersion: 'Version:',
		about: 'About',
		appUpdate: 'App version update',
		cancelUpdate: 'Cancel the update',
		confirmUpdate: 'Confirm update',
		iosUpdate: 'Go to AppStore to update',
		androidUpdate: 'Download and install',
		updateInProgress: 'In the updated version ..',
		importantNotice:
			'Important notice: This major version update will result in data reset. Please make sure to back up all important data before updating.',
		newVersionAvailable: 'The latest version of the aaPanel App has been released.',
		versionNotes: 'Version update instructions:',
		newVersionDetected: 'Detected a new version [{version}], it is recommended to update to the new version!',
		general: 'Convention',
		security: 'Security',
		graphicUnlock: 'Graphic Unlock',
		enabled: 'Opened',
		disabled: 'Disabled',
		modifyGraphicPassword: 'Change graphic unlock password',
		aboutBtApp: 'About aaPanel APP',
		privacyPolicy: 'Privacy Policy',
		serviceAgreement: 'Service agreement',
		checkingUpdate: 'Checking for updates',
		latestVersion: 'The current version is already the latest version',
		closeConfirm: 'Close confirmation',
		confirmCloseUnlock: 'Do you want to turn off graphic unlocking?',
		closeSuccess: 'Close successfully!',
		enableGraphicUnlockFirst: 'Please unlock the graphics first!',
		feedback: 'Feedback',
		recordNumber: 'APP registration number: Guangdong ICP No. 17030143-3A',
		queryLink: 'Search link:',
	},
	scan: {
		scanSuccess: 'Scan code successfully',
		scanFail: 'Scan code failed',
		scanCancel: 'Scan the code to cancel',
		bindSuccess: 'Binding successful',
		bindFail: 'Binding failed',
		loginSuccess: 'Login succeeded',
		loginFail: 'Login failed',
	},
	blank: {
		lock: 'Graphic Unlock',
		unlock: 'Unlocked successfully',
		unlockFail: 'Unlock failed',
		unlockCancel: 'Unlock Cancel',
		notSet: 'Not set temporarily',
		back: 'Back',
		forget: 'Forgot password',
		notChange: 'Do not change password temporarily',
		reset: 'Restore Confirmation',
		resetApp: 'Restore the app ..',
		resetPwd: 'Please draw the original gesture password',
		lockPwd: 'Please unlock',
		confirmApp: 'Are you sure to use this gesture as the app unlock password?',
		forgetConfirm:
			'Forgetting your password will unbind all servers bound to your app and restore the app to its default settings! Are you sure about the operation?',
		blankTitle: 'Fortress tower, ensuring safe and efficient operation and maintenance',
		drawPattern: 'Please draw an unlock pattern with no less than 4 dots',
		patternError: 'Pattern error, please try again',
		welcome: 'Welcome!',
		drawAgain: 'Draw the pattern again for confirmation',
		patternCompleted: 'Pattern drawing completed, click OK below to save!',
		patternMismatch: 'The patterns are inconsistent twice, please try again',
		patternTooShort: 'The pattern drawing cannot be less than 4 points, please try again',
		drawNewPattern: 'Please draw a new password with no less than 4 dots',
		originalPatternError: 'The original password is incorrect! try again',
		resetSuccess: 'Restoration successful!',
		saveSuccess: 'Saved successfully!',
		addServer: 'aaPanel',
	},
	appUpdate: {
		gettingVersion: 'Obtaining ..',
		checkUpdateFailed: 'Check update failed:',
		updateCompleted: 'The update is complete and the app will be restarted',
		installFailed: 'Installation failed:',
	},
	checkAuth: {
		title: 'One-Time Password',
		copyCode: 'Copy verification code',
		rename: 'Rename',
		delete: 'Delete',
		emptyTip: 'There is currently no password or authentication available ..',
		checkTutorial: 'Click on me to view the beginner tutorial',
		securityTip: 'Used for secondary verification of the login panel to ensure panel security',
		scanToAdd: 'Click to scan and add',
		supportTip: 'PS: Supports dynamic passwords and SSH secondary authentication',
		longPressTip: 'Support long press operation',
		modify: 'Modify',
		inputName: 'Please enter a name',
		deleteConfirm: 'Delete confirmation',
		deleteWarning:
			'To delete dynamic passwords in the app, it is recommended to first disable the dynamic password authentication function in the panel before deleting them. Otherwise, you will need to connect to SSH to disable password authentication.',
		addSuccess: 'Added successfully!',
		modifySuccess: 'Modified successfully',
		deleteSuccess: 'Delete successfully',
		unrecognizedQRCode: 'Unrecognized QR code, cannot be added!',
		timeoutText: 'Unrecognized QR code?',
		copySuccess: 'Replicating Success',
	},
	linux: {
		switchPanel: 'SwitchBoard',
		runningTime: 'Continuous operation',
		load: 'Load',
		memory: 'Memory',
		disk: 'Disk',
		multiDiskTips: 'If there are multiple disks, you can swipe left and right to view them',
		website: 'Website',
		database: 'Database',
		securityRisk: 'Security risks',
		modulePlugin: 'Module/Plugin',
		networkFlow: 'Network',
		diskIO: 'Disk IO',
		all: 'All',
		fileManagement: 'Files',
		security: 'Security',
		terminal: 'Terminal',
		monitoring: 'Bonitor',
		blocked: 'Blocking',
		slow: 'Slow',
		normal: 'Normal',
		smooth: 'Smooth',
		unitKBS: 'Unit: KB/s',
		unitMBS: 'Unit: MB/s',
		cores: 'Cores',
		control: {
			title: 'Monitoring management',
			monitorSwitch: 'monitor switches',
			resourceUsage: 'Resource usage',
			loadDetails: 'Load details',
			average: 'Average value',
			maximum: 'Maximum value',
			today: 'Today',
			yesterday: 'Yesterday',
			readBytes: 'Read byte count',
			writeBytes: 'Write byte count',
			networkIO: 'Network IO',
			processName: 'Process Name',
			cpuUsage: 'CPU usage',
			startUser: 'Activate user',
			resourceUsagePercent: 'Resource utilization rate%',
			percentUnit: 'percentage',
			time: 'time',
			minutes1: '1 minute',
			minutes5: '5 minutes',
			minutes15: '15 minutes',
		},
		network: {
			up: 'Upstream',
			down: 'Downstream',
		},
		install: {
			selectVersion: 'Select Version',
			installProcess: 'Installation process',
			completeInstallation: 'Complete installation',
			speedInstall: 'Fast installation',
			installLog: 'Installation log',
			installing: 'Installing ..',
			error: 'An error occurred',
			progress: 'Installation progress',
			retry: 'Retry ',
			complete: 'Complete',
			previous: 'Previous',
			next: 'Next',
			installSuccess: 'Installed successfully',
			successMessage: 'Successfully installed and configured',
			details: {
				version: 'Edition:',
				status: 'Status:',
				running: 'Running',
			},
			logs: {
				startInstall: 'Start installation',
				preparing: 'Preparing to install the environment ..',
				installComplete: 'Installation completed!',
				installFailed: 'Installation failed',
				error: 'An error occurred during the installation process:',
			},
			return: 'Back',
		},
		io: {
			read: 'read',
			write: 'write',
		},
	},
	website: {
		management: 'WebSite',
		running: 'Running',
		stopped: 'Stopped',
		noWebServer: 'The web server is not installed',
		refreshSuccess: 'Refresh successful',
		editRemark: 'Modify remarks',
		editName: 'Change name',
		delete: 'Delete',
		warning: 'Tips',
		confirmDelete: 'Are you sure you want to delete {name}?',
		modify: 'Modify',
		deleting: 'Deleting ..',
		status: {
			notDeployed: 'Not deployed',
			expired: 'Expired',
			certExpired: 'SSL has expired',
			sslExpireDays: 'SSL expiration: {days}',
		},
		errors: {
			keyRequired: 'Please fill in the key!',
			certificateRequired: 'Please fill in the certificate (PEM format)!',
		},
		install: {
			installServer: 'Install the selected server',
			nginx: {
				title: 'Install Nginx',
				description: 'A high-performance, low memory consuming web server',
			},
			apache: {
				title: 'Install Apache',
				description: 'A feature rich, mature and stable web server',
			},
		},
		manage: {
			domainManagement: 'Domain',
			sslManagement: 'SSL',
			closeSSL: 'Turn off SSL',
			certificateFolder: 'SSL folder',
			forceHTTPS: 'Force HTTPS',
			saveCertificate: 'Save and enable certificate',
			certificateDetails: {
				brand: 'Brand:',
				domains: 'Certified Domain:',
				expiryDate: 'Expiration:',
				deploymentStatus: 'Status',
				autoRenewal: 'We will attempt to automatically renew within one month from the expiration date',
				renewalReminder: 'Please replace the certificate with a new one before it expires',
				deployed: 'Successfully deployed',
			},
			keyTitle: 'Key',
			certificateTitle: 'Certificate (PEM format)',
			certificateSaving: 'The certificate is being saved ..',
			closingCertificate: 'Close the certificate ..',
			closeSSLSuccess: 'SSL successfully closed!',
			port: 'Port:',
			cannotBeOperated: 'Not operable',
			sslFolder: {
				certificateBrand: 'Brand:',
				certifiedDomains: 'Certified Domain:',
				expiryDate: 'Expiration:',
				location: 'Location:',
				cloud: 'Cloud',
				local: 'Local',
				deploy: 'Deploy',
				delete: 'Delete',
				deleteConfirm: 'Are you sure to remove the {subject} certificate from the certificate folder?',
				deploying: 'Deploying certificates ..',
				deleting: 'Delete the certificate ..',
			},
		},
	},
	database: {
		management: 'Database',
		copyPassword: 'Copy',
		modifyRemark: 'Modify',
		username: 'Username',
		location: 'Location',
		backup: 'Backups',
		backupExists: 'There is a backup ({count})',
		noBackup: 'Not backup',
		remark: 'Remark',
		password: 'Password',
		noPassword: 'No password',
		passwordCopied: 'Password copied',
		confirmDelete: 'Are you sure you want to delete database {name}?',
		noPasswordSet: 'No password set',
		localDatabase: 'Local Database',
		databaseServiceNotInstalled: 'Database service not installed',
		installMySQL: 'Install MySQL',
		mysqlDescription: 'The most popular open-source relational database with stable performance',
		installSelectedDatabase: 'Install the selected database',
	},
	editor: {
		unsaved: 'Not saved',
		save: 'Save',
		dontSave: 'Do not save',
		fileModified: 'Detected that the file has been modified, do you want to save it?',
		saveSuccess: 'Successfully saved',
		saveFail: 'Save failed',
		noChange: 'The file has not been modified, no need to save!',
		saving: 'Saving in progress ..',
		getContentFailed: 'Failed to retrieve file content',
	},
	files: {
		rootDirectory: 'Root directory',
		newFolder: 'New folder',
		newFile: 'New file',
		modifyRemark: 'Modify remarks',
		confirmDelete: 'Are you sure you want to delete {name}?',
		enterRemark: 'Please enter a note',
		create: 'Create',
		enterFolderName: 'Please enter the folder name',
		enterFileName: 'Please enter the file name',
		recycleError: 'This is the recycle bin directory and cannot be opened',
		imageNotSupported: 'The image file cannot be opened temporarily',
		compressNotSupported: 'The compressed file cannot be opened temporarily',
		mediaNotSupported: 'Video and audio files cannot be opened temporarily',
		systemFileError: 'The system directory file does not support editing',
		fileTypeNotSupported: 'This type of file editing is currently not supported',
		fileSizeTooLarge: 'The file size exceeds 3M and online editing is currently not supported!',
		nameEmpty: 'The name cannot be empty',
		invalidChars: 'The name cannot contain the following characters: /: *?  " < > |',
		nameNoChange: 'The file name has not changed',
		renaming: 'Renaming, please wait ..',
		deleting: 'Deleting ..',
		creatingFolder: 'Creating folder, please wait ..',
		creatingFile: 'Creating file, please wait ..',
	},
	firewall: {
		title: 'Scurity',
		firewallSwitch: 'Firewall switch',
		block: 'Block',
		allow: 'Allow',
		delete: 'Delete',
		port: 'Port',
		ipv6: '（ipv6）',
		status: 'status',
		strategy: 'Strategy',
		note: 'Remark',
		notUsed: 'Not used',
		externalNetworkDisconnected: 'The external network is not accessible',
		normal: 'Normal',
		enableFirewall: 'Enable system firewall',
		enablePrompt:
			'Recommended to enable. Enabling the system firewall can better protect the current server security. Do you want to continue?',
		disableFirewall: 'Disable the system firewall',
		disablePrompt:
			'Disabling the system firewall will cause the server to lose its security protection. Do you want to continue operating?',
		deleteRuleConfirm: 'Are you sure you want to delete the rule for port {port}?',
		loading: 'LOADING',
		setStatusFailed: 'Failed to set firewall status',
		portList: {
			mysql: 'MySQL service default port',
			phpmyadmin: 'Default port for PHPMyAdmin',
			ssh: 'SSH Remote Service',
			ftpData: 'FTP active mode data port',
			ftp: 'FTP protocol default port',
			ftpPassiveRange1: 'FTP Passive Mode Port Range',
			ftpPassiveRange2: 'FTP Passive Mode Port Range',
			memcached: 'Memcached service port',
			rsync: 'Rsync data synchronization service',
			panel: 'aaPanel Linux panel default port',
		},
	},
};
