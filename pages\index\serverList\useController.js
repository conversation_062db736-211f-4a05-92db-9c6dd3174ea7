import { getDevicesInfo } from '@/utils/common.js';
import { useConfigStore } from '@/store';
import { getServerList as getServerListApi } from '@/api/serverList';
import { PATH, UUID, SECRET, AESKEY, SYSTEM } from '@/utils/config';
import { useServerListStore } from './store';
import { hasKey, getPercentage, getByteUnit } from '@/utils';
import Message from '@/hooks/useMessage';
import { ref, nextTick } from 'vue';
import { triggerVibrate } from '@/utils/common';
import { handleServerInfo as handleServerInfoData } from '@/linux/index/useController';
import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import { $t } from '@/locale/index.js';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

export const paging = ref(null);
export const pageContainer = ref(null);
export const iosPromptModel = ref(false);
export const harmonyPromptModel = ref(false);
export const harmonyInfo = ref({});
export const harmonyDeviceInfo = ref({});

export const getDevicesInfoEvent = () => {
	const { phoneBrand, phoneModel } = useConfigStore().getReactiveState();
	getDevicesInfo().then((res) => {
		phoneBrand.value = res.brand;
		phoneModel.value = res.model;
	});
};

export const confirmIosPrompt = (close) => {
	uni.navigateTo({
		url: '/pages/novice/novice?type=ios',
		animationType: 'zoom-fade-out',
		success: () => {
			close && close();
		},
	});
};

export const handleCloudBindSuccess = (cloudCode) => {
	const { serverInit, serverList } = useServerListStore().getReactiveState();
	serverList.value = serverInit.value;
	console.log('云控绑定成功:', cloudCode);
};

// 添加一个标志来防止在手动添加服务器时被下拉刷新干扰
let isManuallyAddingServer = false;

export const handleBindSuccess = async (bindInfo) => {
	console.log('服务器绑定成功，开始处理新服务器:', bindInfo);

	try {
		// 设置标志，防止下拉刷新干扰
		isManuallyAddingServer = true;

		// 处理新添加的服务器项
		const newServerItem = await handleServerItem(bindInfo);

		if (newServerItem) {
			const { serverList } = useServerListStore().getReactiveState();

			// 确保新服务器有稳定的标识符
			newServerItem.uniqueId = `${newServerItem.ip}_${bindInfo.panelPath}`;
			newServerItem.uniqueKey = `${newServerItem.ip}_${bindInfo.panelPath}`;

			// 检查是否已存在相同IP的服务器
			const existingIndex = serverList.value.findIndex((server) => server.ip === newServerItem.ip);

			if (existingIndex === -1) {
				// 新服务器不存在，添加到列表首部
				// 注意：configList 中新服务器已经通过 unshift 添加到首部了
				serverList.value.unshift(newServerItem);
			} else {
				// 服务器已存在，移动到首部并更新数据
				// 先移除现有的服务器
				const existingServer = serverList.value.splice(existingIndex, 1)[0];

				// 保持原有的关键标识符以确保 Vue 组件复用
				newServerItem.uniqueKey = existingServer.uniqueKey || newServerItem.uniqueKey;
				newServerItem.uniqueId = existingServer.uniqueId || newServerItem.uniqueId;
				if (existingServer.zp_index !== undefined) {
					newServerItem.zp_index = existingServer.zp_index;
				}

				// 添加到首部
				serverList.value.unshift(newServerItem);
			}

			// 如果有排序设置，将新服务器添加到排序列表的首部
			const savedSortOrder = uni.getStorageSync('serverSortOrder');
			if (savedSortOrder && Array.isArray(savedSortOrder)) {
				// 检查新服务器是否已在排序列表中
				if (!savedSortOrder.includes(newServerItem.rawPath)) {
					// 将新服务器添加到排序列表首部
					savedSortOrder.unshift(newServerItem.rawPath);
					uni.setStorageSync('serverSortOrder', savedSortOrder);
				}
			}

			// 使用 complete 方法重新设置整个列表
			if (paging.value) {
				paging.value.complete(serverList.value, true);
			}
		}
	} catch (error) {
		console.error('处理新服务器失败:', error);
		// 如果处理失败，则刷新整个列表
		setTimeout(() => {
			if (paging.value) {
				paging.value.reload();
			}
		}, 500);
	} finally {
		// 重置标志
		setTimeout(() => {
			isManuallyAddingServer = false;
		}, 1000);
	}
};

// 导出标志供其他地方使用
export const getIsManuallyAddingServer = () => isManuallyAddingServer;

export const getServerList = async () => {
	uni.showLoading({
		title: $t('common.loading'),
		mask: true,
	});
	try {
		const { configList } = useConfigStore().getReactiveState();
		const { serverInit, serverList } = useServerListStore().getReactiveState();

		// 保存当前已有的服务器列表，用于保持顺序
		const existingServers = [...serverList.value];
		const existingServerPaths = new Set(existingServers.map((server) => server.rawPath));

		// 找出新增的配置项（不在现有服务器列表中的）
		const newConfigItems = configList.value.filter((config) => !existingServerPaths.has(config.panelPath));

		// 更新现有服务器的数据
		const updatePromises = existingServers.map(async (existingServer) => {
			const configItem = configList.value.find((config) => config.panelPath === existingServer.rawPath);
			if (configItem) {
				try {
					const updatedServerData = await handleServerItem(configItem);
					if (updatedServerData) {
						// 保持原有的 uniqueKey、uniqueId 和其他关键标识符，确保 Vue 组件复用
						updatedServerData.uniqueKey =
							existingServer.uniqueKey || `${updatedServerData.ip}_${configItem.panelPath}`;
						updatedServerData.uniqueId = existingServer.uniqueId || `${updatedServerData.ip}_${Date.now()}`;
						// 保持 zp_index 以确保虚拟列表的稳定性
						if (existingServer.zp_index !== undefined) {
							updatedServerData.zp_index = existingServer.zp_index;
						}
					}
					return updatedServerData;
				} catch (error) {
					console.error(`更新现有服务器失败: ${error}`);
					return existingServer; // 如果更新失败，保持原有数据
				}
			}
			return null; // 配置项不存在，标记为删除
		});

		// 处理新增的服务器
		const newServerPromises = newConfigItems.map(async (item, index) => {
			try {
				const serverData = await handleServerItem(item);
				if (serverData) {
					serverData.uniqueId = `${serverData.ip}_${Date.now()}_${index}`;
					serverData.uniqueKey = `${serverData.ip}_${item.panelPath}`;
				}
				return serverData;
			} catch (error) {
				console.error(`处理新服务器项失败: ${error}`);
				return null;
			}
		});

		// 等待所有更新和新增操作完成
		const [updatedResults, newResults] = await Promise.all([
			Promise.all(updatePromises),
			Promise.all(newServerPromises),
		]);

		// 构建最终的服务器列表：先是新增的服务器，然后是更新后的现有服务器
		const finalServerList = [];

		// 在首部添加新增的服务器
		newResults.filter((server) => server !== null).forEach((server) => finalServerList.push(server));

		// 添加更新后的现有服务器（过滤掉null值，即已删除的配置）
		updatedResults.filter((server) => server !== null).forEach((server) => finalServerList.push(server));

		// 去重并更新服务器列表
		const uniqueServerList = deduplicateServerList(finalServerList);
		serverList.value = uniqueServerList;

		// 应用用户设置的排序顺序
		// 首先检查是否有保存的排序顺序
		const savedSortOrder = uni.getStorageSync('serverSortOrder');

		if (savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
			// 如果有保存的排序顺序，按照该顺序排列
			const orderedServerList = [];
			const remainingServers = [...serverList.value];

			// 按照保存的顺序添加服务器 - 使用 rawPath 匹配
			savedSortOrder.forEach((rawPath) => {
				const index = remainingServers.findIndex((server) => server.rawPath === rawPath);
				if (index !== -1) {
					orderedServerList.push(remainingServers.splice(index, 1)[0]);
				}
			});

			// 添加不在排序列表中的新服务器到首部
			orderedServerList.unshift(...remainingServers);

			serverList.value = orderedServerList;
		} else {
			// 如果没有保存的排序顺序，按照 configList 的顺序排列
			const orderedServerList = [];

			// 按照 configList 的顺序重新排列 serverList
			configList.value.forEach((config) => {
				const server = serverList.value.find((s) => s.rawPath === config.panelPath);
				if (server) {
					orderedServerList.push(server);
				}
			});

			// 添加任何不在 configList 中的服务器（理论上不应该有）
			serverList.value.forEach((server) => {
				if (!orderedServerList.find((s) => s.ip === server.ip)) {
					orderedServerList.unshift(server); // 添加到首部
				}
			});

			serverList.value = orderedServerList;
		}

		return serverList.value;
	} catch (error) {
		console.log(error);
		return [];
	} finally {
		uni.hideLoading();
	}
};

// 新增：服务器列表去重函数
const deduplicateServerList = (serverList) => {
	const seen = new Set();
	return serverList.filter((server) => {
		const key = server.ip; // 使用IP作为唯一标识
		if (seen.has(key)) {
			return false;
		}
		seen.add(key);
		return true;
	});
};

export const handleServerItem = async (item) => {
	const { serverInit } = useServerListStore().getReactiveState();
	const { panelPath, panelKey, aesKey, uuid, system } = item;

	// 设置全局变量
	PATH.value = panelPath;
	UUID.value = uuid;
	SECRET.value = panelKey;
	AESKEY.value = aesKey;
	SYSTEM.value = system;

	// 准备服务器对象 - 使用深拷贝避免修改原始数据
	let initJson = JSON.parse(JSON.stringify(serverInit.value)),
		initPath = panelPath;
	initJson[0].name = $t('server.connecting');
	initJson[0].ip = initPath.substring(initPath.lastIndexOf('/') + 1, initPath.lastIndexOf(':'));
	initJson[0].rawPath = initPath;
	initJson[0].status = true;

	let temp = JSON.stringify(initJson);
	temp = temp.replace(/\[|]/g, '');
	let serverItem = JSON.parse(temp);

	// 为服务器项添加稳定的唯一标识符，用于Vue的key绑定
	serverItem.uniqueKey = `${serverItem.ip}_${initPath}`;

	// 创建一个Promise用于处理API请求和超时
	return new Promise((resolve) => {
		// 设置超时处理
		let setTime = setTimeout(() => {
			serverItem.name = $t('common.fail');
			serverItem.status = false;
			resolve(serverItem);
		}, 5000);
		// 发起API请求
		getServerListApi()
			.then((res) => {
				clearTimeout(setTime);
				// 处理API响应
				if (hasKey(res, 'data')) {
					if (res.data && res.data.msg) {
						Message.info(res.data.msg, {
							duration: 6000,
						});
					}
					serverItem.status = false;
				} else {
					// 成功获取服务器信息，更新服务器对象
					serverItem.status = true;
					serverItem.name = res.title;

					let tempTime = res.time;
					if (serverItem.system == 'windows') {
						tempTime = tempTime.substr(0, tempTime.indexOf('天') + 1);
					}
					serverItem.uptime = tempTime;
					serverItem.network.up = res.up;
					serverItem.network.down = res.down;

					// 处理CPU数据
					serverItem.cpu.usage = res.cpu[0]?.toFixed(1);
					serverItem.cpu.cores = res.cpu[1];

					// 处理负载数据
					let loadCount =
						Math.round((res.load.one / res.load.max) * 100) > 100
							? 100
							: Math.round((res.load.one / res.load.max) * 100);
					loadCount = loadCount < 0 ? 0 : loadCount;
					const loadInfo = handleServerInfoData(loadCount, 'load');
					serverItem.load.usage = loadInfo.val?.toFixed(1);
					serverItem.load.total = loadInfo.title;

					// 处理内存数据
					serverItem.memory.usage = getPercentage(res.mem.memTotal, res.mem.memRealUsed)?.toFixed(1);
					if (DEFAULT_API_TYPE === 'domestic') {
						serverItem.memory.total = res.mem.memNewTotal;
					} else {
						serverItem.memory.total = `${res.mem.memTotal} MB`;
					}

					// 磁盘
					let zeroRatio = res.disk[0].size[3];
					zeroRatio = parseFloat(zeroRatio.substring(0, zeroRatio.lastIndexOf('%')));
					serverItem.disk.usage = zeroRatio?.toFixed(1);
					serverItem.disk.total = res.disk[0].size[0];
				}

				// 注释掉直接更新 serverList 的逻辑，避免重复添加
				// 数据更新将由调用方统一处理
				resolve(serverItem);
			})
			.catch((error) => {
				clearTimeout(setTime);
				serverItem.name = $t('common.fail');
				serverItem.status = false;
				console.error('获取服务器信息失败:', error);
				resolve(serverItem);
			});
	});
};

export const getServerInfoList = async () => {
	const { configList } = useConfigStore().getReactiveState();
	const { serverList } = useServerListStore().getReactiveState();

	// 使用 Promise.all 来并行处理所有更新，避免数据竞争
	const updatePromises = configList.value.map(async (configItem) => {
		const index = serverList.value.findIndex((serverItem) => serverItem.rawPath === configItem.panelPath);
		if (index !== -1 && serverList.value[index].status) {
			try {
				const res = await handleServerItem(configItem);
				// 确保索引仍然有效（防止在异步操作期间数组被修改）
				if (index < serverList.value.length && serverList.value[index].rawPath === configItem.panelPath) {
					serverList.value[index] = res;
				}
			} catch (error) {
				console.error('更新服务器信息失败:', error);
			}
		}
	});

	await Promise.all(updatePromises);
};

// 图表配置函数
export const getChartColorStops = (usage) => {
	if (usage >= 91) {
		// 81-100% 显示纯红色
		return [
			{ offset: 0, color: '#E53935' }, // 红色
			{ offset: 1, color: '#E53935' }, // 红色
		];
	} else if (usage >= 51) {
		// 51-80% 从绿色到黄色的渐变
		return [
			{ offset: 0, color: '#4CAF50' }, // 绿色起点
			{ offset: 1, color: '#FFEB3B' }, // 黄色终点
		];
	} else {
		// 0-50% 纯绿色
		return [
			{ offset: 0, color: '#4CAF50' }, // 绿色
			{ offset: 1, color: '#4CAF50' }, // 绿色
		];
	}
};

// 获取基础图表配置
export const getBaseChartConfig = (usage, label) => {
	// 确保usage是数字
	usage = parseFloat(usage) || 0;

	// 创建颜色渐变配置
	const colorStops = getChartColorStops(usage);

	// 创建仪表盘数据
	const gaugeData = [
		{
			value: usage,
			name: label,
			title: {
				show: false,
			},
			detail: {
				valueAnimation: true,
				offsetCenter: [0, '0%'],
				fontSize: 10,
				color: '#A7A7A7',
				formatter: '{value}%',
			},
			itemStyle: {
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 0,
					y2: 1,
					colorStops: [...colorStops], // 使用深拷贝
				},
			},
		},
	];

	// 返回图表配置
	return {
		series: [
			{
				type: 'gauge',
				radius: '100%',
				startAngle: 90,
				endAngle: -270,
				pointer: {
					show: false,
				},
				progress: {
					show: true,
					overlap: false,
					roundCap: true,
					clip: false,
					itemStyle: {
						borderWidth: 1,
					},
				},
				axisLine: {
					lineStyle: {
						width: 4,
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					show: false,
					fontSize: 9,
				},
				title: {
					show: false,
				},
				data: gaugeData,
				detail: {
					valueAnimation: true,
					fontSize: 10,
					color: '#A7A7A7',
					formatter: '{value}%',
					borderRadius: 20,
				},
				animation: true,
				animationDuration: 1500,
				animationDurationUpdate: 1000,
				animationEasing: 'cubicInOut',
			},
		],
	};
};

// CPU图表配置
export const getCpuChartData = (cpuData) => {
	return getBaseChartConfig(cpuData.usage, $t('server.cpu'));
};

// 内存图表配置
export const getMemChartData = (memData) => {
	return getBaseChartConfig(memData.usage, $t('server.memory'));
};

// 磁盘图表配置
export const getDiskChartData = (diskData) => {
	return getBaseChartConfig(diskData.usage, $t('server.disk'));
};

// 服务器列表轮询
export const useServerListPolling = () => {
	let pollingTimer = null;

	// 开始轮询
	const startPolling = () => {
		// 先清除可能存在的定时器
		stopPolling();

		// 立即执行一次
		getServerInfoList();

		// 设置定时器，每6秒执行一次，确保动画有足够的完成时间
		pollingTimer = setInterval(() => {
			getServerInfoList();
		}, 3000);
	};

	// 停止轮询
	const stopPolling = () => {
		if (pollingTimer) {
			clearInterval(pollingTimer);
			pollingTimer = null;
		}
	};

	return {
		startPolling,
		stopPolling,
	};
};

// 删除相关
export const deleteModel = ref(false);

export const confirmDelete = (close) => {
	close();
	onActionDelete(activeServer.value);
	setTimeout(() => {
		hideContextMenu();
	}, 200);
	pageContainer.value.notify.success($t('common.deleteSuccess'));
};

export const onActionDelete = (item) => {
	const { serverList } = useServerListStore().getReactiveState();
	const { configList } = useConfigStore().getReactiveState();
	const configIndex = configList.value.findIndex((configItem) => configItem.panelPath === item.rawPath);
	const serverIndex = serverList.value.findIndex((serverItem) => serverItem.ip === item.ip);

	// 从服务器列表中删除
	if (serverIndex !== -1) {
		serverList.value.splice(serverIndex, 1);
	}

	// 从配置列表中删除
	if (configIndex !== -1) {
		configList.value.splice(configIndex, 1);
		uni.setStorageSync('configList', configList.value);
	}

	// 同步更新排序列表，移除已删除的服务器
	const savedSortOrder = uni.getStorageSync('serverSortOrder');
	if (savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
		const updatedSortOrder = savedSortOrder.filter((rawPath) => rawPath !== item.rawPath);
		if (updatedSortOrder.length !== savedSortOrder.length) {
			// 只有当排序列表确实发生变化时才更新
			uni.setStorageSync('serverSortOrder', updatedSortOrder);
		}
	}
};

export const navigateServerInfo = (server, callback) => {
	throttle(() => {
		const { status, name } = server;
		if (!status || name === $t('server.connecting')) {
			return pageContainer.value.notify.error($t('server.serverConnectionFailed'));
		}
		handleServerInfo(server);
		if (SYSTEM.value !== 'linux') {
			return pageContainer.value.notify.error('windows系统暂未开放');
		}
		callback && callback();
	});
};

export const handleServerInfo = (server) => {
	const { rawPath } = server;
	const { configList } = useConfigStore().getReactiveState();
	const item = configList.value.find((configItem) => configItem.panelPath === rawPath);
	const { panelPath, panelKey, aesKey, uuid, system } = item;
	PATH.value = panelPath;
	UUID.value = uuid;
	SECRET.value = panelKey;
	AESKEY.value = aesKey;
	SYSTEM.value = system;
	getCurrentServer(server);
};

// 获取当前选中的服务器
export const getCurrentServer = (server) => {
	const { currentServerInfo } = useConfigStore().getReactiveState();
	currentServerInfo.value = server;
	return server;
};

export const navigateServer = (server) => {
	navigateServerInfo(server, () => {
		uni.navigateTo({
			url: `/linux/index/index`,
			animationType: 'zoom-fade-out',
		});
	});
};

// 长按菜单相关
export const showContextMenu = ref(false);
export const activeServer = ref(null);
export const activeIndex = ref(-1);
export const menuPosition = ref({
	top: '0px',
	left: '0px',
	class: '',
});
export const clonePosition = ref({
	top: '0px',
	left: '0px',
	width: '0px',
	height: '0px',
});

// 触摸状态管理
export const touchStartTime = ref(0);
export const touchStartPos = ref({ x: 0, y: 0 });
export const isTouchMoved = ref(false);
export const longPressTimer = ref(null);
export const LONG_PRESS_THRESHOLD = 600; // 长按阈值，单位毫秒
export const MOVE_THRESHOLD = 10; // 移动阈值，单位像素

// 菜单高度管理
export const actualMenuHeight = ref(80); // 预估高度，实际会测量
export const showTempMenu = ref(false);

// 测量菜单高度的方法
export const measureMenuHeight = () => {
	// 显示临时测量菜单
	showTempMenu.value = true;

	// 等待临时菜单渲染完成
	nextTick(() => {
		uni.createSelectorQuery()
			.select('.temp-measure-menu')
			.boundingClientRect((rect) => {
				if (rect && rect.height > 0) {
					actualMenuHeight.value = rect.height;
				}
				// 隐藏临时菜单
				showTempMenu.value = false;
			})
			.exec();
	});
};

// 触摸处理相关函数
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
	}

	// 记录触摸开始时间和位置
	touchStartTime.value = Date.now();
	touchStartPos.value = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved.value = false;

	// 设置长按定时器
	longPressTimer.value = setTimeout(() => {
		if (!isTouchMoved.value) {
			const index = event.currentTarget.dataset.index;
			const serverData = JSON.parse(event.currentTarget.dataset.server);
			showFloatingMenu(serverData, event, index);
		}
	}, LONG_PRESS_THRESHOLD);
};

export const handleTouchMove = (event) => {
	if (!touchStartPos.value) return;

	// 计算移动距离
	const moveX = Math.abs(event.touches[0].clientX - touchStartPos.value.x);
	const moveY = Math.abs(event.touches[0].clientY - touchStartPos.value.y);

	// 如果移动超过阈值，标记为已移动并取消长按定时器
	if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
		isTouchMoved.value = true;

		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
	}
};

export const handleTouchEnd = (event) => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}

	// 如果未移动且是短触摸（非长按），则打开文件
	if (!isTouchMoved.value && Date.now() - touchStartTime.value < LONG_PRESS_THRESHOLD) {
		const serverData = JSON.parse(event.currentTarget.dataset.server);
		navigateServer(serverData);
	}
};

export const handleTouchCancel = () => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

// 显示悬浮菜单 - 两阶段定位
export const showFloatingMenu = (server, event, index) => {
	// 触感反馈
	triggerVibrate();

	activeServer.value = server;
	activeIndex.value = index;

	// 获取系统信息，用于检测是否会超出屏幕
	const systemInfo = uni.getSystemInfoSync();
	const screenHeight = systemInfo.windowHeight;
	const screenWidth = systemInfo.windowWidth;

	// 获取被长按元素相对于页面的位置
	uni.createSelectorQuery()
		.selectAll('.server-item')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];

			// 设置克隆项位置
			clonePosition.value = {
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
			};

			// 预估参数
			const tabbarHeight = 60; // 底部导航栏高度
			const headerHeight = 50; // 顶部标题栏高度
			const menuWidth = 120; // 菜单宽度
			const edgeBuffer = 10; // 边缘安全距离

			// 计算菜单位置
			let menuTop,
				menuLeft,
				menuClass = '';

			// 水平定位 - 居中显示，但保持在屏幕内
			menuLeft = rect.left + rect.width / 2;
			// 防止菜单超出屏幕左侧
			if (menuLeft - menuWidth / 2 < edgeBuffer) {
				menuLeft = menuWidth / 2 + edgeBuffer;
			}
			// 防止菜单超出屏幕右侧
			if (menuLeft + menuWidth / 2 > screenWidth - edgeBuffer) {
				menuLeft = screenWidth - menuWidth / 2 - edgeBuffer;
			}

			// 垂直定位 - 智能判断上方还是下方
			// 计算下方可用空间和上方可用空间
			const spaceBelow = screenHeight - rect.bottom - tabbarHeight;
			const spaceAbove = rect.top - headerHeight;

			// 优先考虑下方显示，如果下方空间不足，再考虑上方
			if (spaceBelow >= actualMenuHeight.value + edgeBuffer) {
				// 下方有足够空间
				menuTop = rect.bottom + edgeBuffer;
				menuClass = 'menu-bottom';
			} else if (spaceAbove >= actualMenuHeight.value + edgeBuffer) {
				// 上方有足够空间 - 菜单底部紧贴克隆项顶部
				menuTop = rect.top - actualMenuHeight.value - edgeBuffer;
				menuClass = 'menu-top menu-position-bottom'; // 添加菜单位置标记
			} else {
				// 两边都没有理想空间，选择空间较大的一边
				if (spaceBelow >= spaceAbove) {
					// 使用下方剩余空间
					menuTop = rect.bottom + edgeBuffer;
					menuClass = 'menu-bottom';
				} else {
					// 使用上方剩余空间 - 菜单底部紧贴克隆项顶部
					menuTop = rect.top - actualMenuHeight.value;
					menuClass = 'menu-top menu-position-bottom';
				}
			}

			// 设置菜单初始位置和样式
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
			};

			// 显示菜单
			showContextMenu.value = true;

			// 第二阶段：在菜单渲染后微调位置
			nextTick(() => {
				// 获取实际菜单高度
				uni.createSelectorQuery()
					.select('.context-menu')
					.boundingClientRect((menuRect) => {
						if (!menuRect) return;

						const actualMenuHeight = menuRect.height;

						// 如果菜单显示在上方，需要向上偏移菜单高度
						if (menuClass.includes('menu-position-bottom')) {
							// 确保菜单底部与克隆项顶部对齐
							const adjustedTop = rect.top - actualMenuHeight;
							menuPosition.value.top = `${adjustedTop}px`;
						}

						// 如果实际菜单宽度与预估不同，调整水平居中
						const actualMenuWidth = menuRect.width;
						if (Math.abs(actualMenuWidth - menuWidth) > 10) {
							// 重新计算水平位置
							let adjustedLeft = rect.left + rect.width / 2;
							if (adjustedLeft - actualMenuWidth / 2 < edgeBuffer) {
								adjustedLeft = actualMenuWidth / 2 + edgeBuffer;
							}
							if (adjustedLeft + actualMenuWidth / 2 > screenWidth - edgeBuffer) {
								adjustedLeft = screenWidth - actualMenuWidth / 2 - edgeBuffer;
							}
							menuPosition.value.left = `${adjustedLeft}px`;
						}
					})
					.exec();
			});
		})
		.exec();
};

// 隐藏悬浮菜单
export const hideContextMenu = () => {
	showContextMenu.value = false;
	activeServer.value = null;
	activeIndex.value = -1;
};

// 确认删除服务器
export const confirmDeleteServer = () => {
	// 使用现有的删除逻辑
	onActionDelete(activeServer.value);
	// 隐藏菜单
	hideContextMenu();
};

export const onActionClick = (e, item) => {
	// e.index 对应着滑动列表的索引
	if (e.index === 0) {
		activeServer.value = item;
		deleteModel.value = true;
	}
};

// 更多菜单相关状态管理
export const showMoreMenu = ref(false);
export const moreMenuPosition = ref({
	top: '0px',
	right: '0px',
});

// 切换更多菜单显示状态
export const toggleMoreMenu = () => {
	if (showMoreMenu.value) {
		hideMoreMenu();
	} else {
		showMoreMenuDropdown();
	}
};

// 显示更多菜单下拉框
export const showMoreMenuDropdown = () => {
	// 获取导航栏右侧按钮的位置
	uni.createSelectorQuery()
		.select('.more-server-icon')
		.boundingClientRect((rect) => {
			if (!rect) return;

			// 计算菜单位置 - 右对齐，位于按钮下方
			const menuTop = rect.bottom + 8; // 位于按钮下方8rpx
			const menuRight = 20; // 距离右边缘20rpx

			moreMenuPosition.value = {
				top: `${menuTop}px`,
				right: `${menuRight}px`,
			};

			showMoreMenu.value = true;
		})
		.exec();
};

// 隐藏更多菜单
export const hideMoreMenu = () => {
	showMoreMenu.value = false;
};

// 处理扫一扫点击
export const handleScanClick = (startScanCallback) => {
	hideMoreMenu();
	// 调用传入的扫描函数
	if (startScanCallback && typeof startScanCallback === 'function') {
		startScanCallback();
	}
};

// IP隐藏状态管理
export const isIPHidden = ref(false);

// 初始化IP隐藏状态
export const initIPHiddenState = () => {
	// 从本地存储读取IP隐藏状态
	const savedState = uni.getStorageSync('isIPHidden');
	isIPHidden.value = savedState === true || savedState === 'true';
};

// 格式化IP显示
export const formatIPDisplay = (ip) => {
	if (!isIPHidden.value) {
		return ip;
	}

	// 隐藏IP，显示为 xxx.xxx.xxx.xxx
	// if (ip && ip.includes('.')) {
	// 	const parts = ip.split('.');
	// 	return parts.map(() => 'xx').join('.');
	// }

	// 如果不是标准IP格式，显示为 xxx
	return 'xxx';
};

// 处理隐藏IP点击
export const handleHideIPClick = () => {
	hideMoreMenu();

	// 切换IP隐藏状态
	isIPHidden.value = !isIPHidden.value;

	// 保存到本地存储
	uni.setStorageSync('isIPHidden', isIPHidden.value);

	// 显示提示信息
	const message = isIPHidden.value ? '已隐藏IP地址' : '已显示IP地址';
	pageContainer.value.notify.success(message);
};

// 处理排序点击
export const handleSortClick = () => {
	hideMoreMenu();

	// 跳转到排序页面
	uni.navigateTo({
		url: '/pages/index/serverList/sort/index',
		animationType: 'slide-in-right',
	});
};
