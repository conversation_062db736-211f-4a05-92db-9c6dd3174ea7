<template>
  <page-container ref="pageContainer" :title="`${currentServerInfo?.name} (${currentDisk})`" :is-back="true">
    <template #nav-left>
      <view class="flex items-center text-26 flex-nowrap text-secondary min-w-100 max-w-280">
        <text class="path-text">{{ navPath }}</text>
      </view>
    </template>
    <template #nav-right>
      <view class="relative">
        <uni-icons
          class="mr-20"
          color="var(--text-color-secondary)"
          type="more-filled"
          size="24"
          @tap="toggleNavMenu"
        ></uni-icons>

        <!-- 导航栏下拉菜单蒙层 -->
        <view
          class="fixed top-0 left-0 right-0 bottom-0 z-39"
          v-if="showNavMenu"
          @tap="hideNavMenu"
          @touchmove.prevent
        ></view>

        <!-- 导航栏下拉菜单 -->
        <view class="nav-dropdown-menu" v-if="showNavMenu" @tap.stop>
          <view class="menu-item" @tap="openCreateFile('folder')">
            <uni-icons fontFamily="iconfont" class="icon-folder-filled" size="16" color="#E6A23C"></uni-icons>
            <text class="ml-20 text-32 text-#666">{{ $t('files.newFolder') }}</text>
          </view>
          <view class="menu-divider"></view>
          <view class="menu-item" @tap="openCreateFile('file')">
            <uni-icons fontFamily="iconfont" class="icon-file" size="16" color="#909399"></uni-icons>
            <text class="ml-20 text-32 text-#666">{{ $t('files.newFile') }}</text>
          </view>
        </view>
      </view>
    </template>
    <view class="p-20">
      <FileHeadTab />

      <!-- 模糊遮罩 -->
      <view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

      <!-- 克隆项容器 - 放在外层，使用fixed定位 -->
      <view class="fixed-clone-container" v-if="showContextMenu">
        <view class="item-clone-wrapper" :style="clonePosition" v-if="activeFile">
          <view class="file-item-clone bg-primary flex items-center">
            <view class="flex justify-center items-center w-80 h-80">
              <uni-icons
                fontFamily="iconfont"
                :class="`icon-${getFileIconType(activeFile.ext, activeFile.icon)}`"
                size="24"
                :color="getFileIconColor(activeFile.ext, activeFile.icon)"
              ></uni-icons>
            </view>
            <view class="flex-1 ml-20">
              <view class="flex items-start">
                <view class="text-primary text-26 text-ellipsis ws-nowrap overflow-hidden max-w-400 min-w-50">{{
                  activeFile.fileName + activeFile.isLink
                }}</view>
                <view class="text-tertiary text-22 ml-10" v-if="activeFile.ps">{{ activeFile.ps }}</view>
              </view>
              <view class="text-tertiary text-22 mt-8">{{ activeFile.time }}</view>
            </view>
            <view class="text-tertiary text-22 mr-10" v-if="activeFile.ext !== 'folder'">{{
              getByteUnit(activeFile.size)
            }}</view>
            <uni-icons
              v-show="activeFile.ext === 'folder'"
              type="right"
              color="var(--text-color-primary)"
              size="20"
              class="mx-2"
            ></uni-icons>
          </view>
        </view>
      </view>

      <!-- 悬浮上下文菜单 -->
      <view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
        <view class="menu-item" @click="showRemarkDialog = true">
          <uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
          <text class="menu-text">{{ $t('files.modifyRemark') }}</text>
        </view>
        <view class="menu-divider"></view>
        <view class="menu-item" @click="showRenameDialog = true">
          <uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
          <text class="menu-text">{{ $t('common.rename') }}</text>
        </view>
        <view class="menu-divider"></view>
        <view class="menu-item menu-delete" @click="showDeleteDialog = true">
          <uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
          <text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
        </view>
      </view>

      <!-- 添加一个隐藏的临时菜单用于测量高度 -->
      <view
        class="temp-measure-menu context-menu"
        v-if="showTempMenu"
        style="position: absolute; opacity: 0; pointer-events: none; top: -9999px"
      >
        <view class="menu-item">
          <uni-icons type="paperclip" size="16" color="#007AFF"></uni-icons>
          <text class="menu-text">{{ $t('files.modifyRemark') }}</text>
        </view>
        <view class="menu-divider"></view>
        <view class="menu-item">
          <uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
          <text class="menu-text">{{ $t('common.rename') }}</text>
        </view>
        <view class="menu-divider"></view>
        <view class="menu-item menu-delete">
          <uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
          <text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
        </view>
      </view>

      <z-paging
        ref="paging"
        class="mt-260"
        :default-page-size="100"
        use-virtual-list
        :force-close-inner-list="true"
        :auto-hide-loading-after-first-loaded="false"
        :auto-show-system-loading="true"
        @virtualListChange="virtualListChange"
        @query="queryList"
        @refresherStatusChange="reload"
        :refresher-complete-delay="200"
      >
        <view class="px-30" :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="item in filesList">
          <view
            class="file-item-container"
            @touchstart="handleTouchStart($event)"
            @touchmove="handleTouchMove($event)"
            @touchend="handleTouchEnd($event)"
            @touchcancel="handleTouchCancel($event)"
            :data-index="item.zp_index"
            :data-file="JSON.stringify(item)"
          >
            <view
              class="py-20 flex items-center file-item"
              :class="{ hidden: showContextMenu && activeIndex === item.zp_index }"
            >
              <view class="flex justify-center items-center w-80 h-80">
                <uni-icons
                  fontFamily="iconfont"
                  :class="`icon-${getFileIconType(item.ext, item.icon)}`"
                  size="24"
                  :color="getFileIconColor(item.ext, item.icon)"
                ></uni-icons>
              </view>
              <view class="flex-1 ml-20">
                <view class="flex items-start">
                  <view class="text-primary text-26 text-ellipsis ws-nowrap overflow-hidden max-w-400 min-w-50">{{
                    item.fileName + item.isLink
                  }}</view>
                  <view class="text-tertiary text-22 ml-10" v-if="item.ps">{{ item.ps }}</view>
                </view>
                <view class="text-tertiary text-22 mt-8">{{ item.time }}</view>
              </view>
              <view class="text-tertiary text-22 mr-10" v-if="item.ext !== 'folder'">{{ getByteUnit(item.size) }}</view>
              <uni-icons
                v-show="item.ext === 'folder'"
                type="right"
                color="var(--text-color-primary)"
                size="20"
                class="mx-2"
              ></uni-icons>
            </view>
          </view>
          <view class="file-divider h-2"></view>
        </view>
      </z-paging>
    </view>
    <uni-fab
      :pattern="{ buttonColor: '#20a50a' }"
      horizontal="right"
      vertical="bottom"
      direction="vertical"
      :popMenu="false"
      @click="clickActionSheet"
    >
    </uni-fab>
    <uv-action-sheet ref="actionSheet" :actions="actionList" :cancelText="$t('common.cancel')" safeAreaInsetBottom @select="handleActionSheet"> </uv-action-sheet>
    <CustomDialog
      contentHeight="200rpx"
      v-model="showDeleteDialog"
      :title="$t('common.tip')"
      :confirmText="$t('common.delete')"
      :confirmStyle="{
        backgroundColor: '#FF3B30',
      }"
      @confirm="confirmDeleteFile"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        {{ $t('files.confirmDelete', { name: activeFile?.fileName }) }}
      </view>
    </CustomDialog>
    <CustomDialog
      contentHeight="100rpx"
      v-model="showRenameDialog"
      :title="$t('common.rename')"
      :confirmText="$t('common.rename')"
      @confirm="renameFile"
      @cancel="renameFileName = ''"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        <uv-input v-model="renameFileName" :placeholder="activeFile?.fileName" />
      </view>
    </CustomDialog>
    <CustomDialog
      contentHeight="100rpx"
      v-model="showRemarkDialog"
      :title="$t('files.modifyRemark')"
      :confirmText="$t('common.modify')"
      @confirm="confirmRemark"
      @cancel="renameRemark = ''"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        <uv-input v-model="renameRemark" :placeholder="activeFile?.ps || $t('files.enterRemark')" />
      </view>
    </CustomDialog>
    <CustomDialog
      contentHeight="100rpx"
      v-model="showCreateDialog"
      :title="$t(createFileType === 'folder' ? 'files.newFolder' : 'files.newFile')"
      :confirmText="$t('files.create')"
      @confirm="confirmCreate"
      @cancel="createFileName = ''"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        <uv-input
          v-model="createFileName"
          :placeholder="$t(createFileType === 'folder' ? 'files.enterFolderName' : 'files.enterFileName')"
        />
      </view>
    </CustomDialog>
  </page-container>
</template>

<script setup>
  import { ref, watch, onMounted, computed } from 'vue';
  import { onUnload, onBackPress } from '@dcloudio/uni-app';
  import PageContainer from '@/components/PageContainer/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import FileHeadTab from './fileHeadTab.vue';
  import { useConfigStore } from '@/store/modules/config';
  import { $t } from '@/locale/index.js';
  import {
    getFilesList,
    currentDisk,
    paging,
    getFileIconType,
    getFileIconColor,
    currentPath,
    pageContainer,
    showContextMenu,
    activeFile,
    activeIndex,
    menuPosition,
    clonePosition,
    showTempMenu,
    measureMenuHeight,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleTouchCancel,
    hideContextMenu,
    renameFile,
    confirmDeleteFile,
    pathList,
    cutDirPath,
    parsePath,
    showDeleteDialog,
    showRenameDialog,
    renameFileName,
    showRemarkDialog,
    renameRemark,
    confirmRemark,
    // 导航菜单相关
    showNavMenu,
    toggleNavMenu,
    hideNavMenu,
    createFileType,
    createFileName,
    showCreateDialog,
    openCreateFile,
    confirmCreate,
    actionSheet,
    actionList,
    handleActionSheet,
    clickActionSheet
  } from './useController';
  import { getByteUnit } from '@/utils/common';

  const { currentServerInfo } = useConfigStore().getReactiveState();

  const filesList = ref([]);

  const virtualListChange = (vList) => {
    filesList.value = vList;
  };

  const queryList = async (page, pageSize) => {
    try {
      const res = await getFilesList(page, pageSize);
      paging.value.complete(res);
      paging.value.updateVirtualListRender();
    } catch (error) {
      paging.value.complete([]);
      console.error(error);
    }
  };

  const navPath = computed(() => {
    const maxLength = 8; // 设置最大字符数限制
    const path =
      pathList.value[pathList.value.length - 1].name === $t('files.rootDirectory')
        ? '/'
        : pathList.value[pathList.value.length - 1].name;

    // 如果路径长度超过限制，截取并添加省略号
    if (path.length > maxLength) {
      return path.substring(0, maxLength) + '...';
    }

    return path;
  });

  const reload = (reloadType) => {
    if (reloadType === 'complete') {
      pageContainer.value.notify.success($t('common.refreshSuccess'));
    }
  };

  // 在页面挂载时获取菜单高度
  onMounted(() => {
    measureMenuHeight();
  });

  // 监听菜单显示状态，防止页面滚动
  watch(showContextMenu, (val) => {
    if (val) {
      // 菜单显示时，禁用页面滚动
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      });

      // 锁定列表滚动
      if (paging.value) {
        paging.value.lockScroll && paging.value.lockScroll(true);
      }

      // 关闭导航下拉菜单
      showNavMenu.value = false;
    } else {
      // 恢复列表滚动
      if (paging.value) {
        paging.value.lockScroll && paging.value.lockScroll(false);
      }
    }
  });

  onBackPress(() => {
    // 如果导航菜单正在显示，先关闭菜单
    if (showNavMenu.value) {
      showNavMenu.value = false;
      return true;
    }

    // 如果长按菜单正在显示，先关闭菜单
    if (showContextMenu.value) {
      hideContextMenu();
      return true;
    }

    if (pathList.value.length === 1) {
      return false;
    }
    cutDirPath(pathList.value[pathList.value.length - 2].path);
    return true;
  });

  onUnload(() => {
    currentPath.value = '/';
    showDeleteDialog.value = false;
    showRenameDialog.value = false;
    showRemarkDialog.value = false;
    showCreateDialog.value = false;
  });
</script>

<style lang="scss" scoped>
  .file-divider {
    background-color: var(--text-color-light);
    opacity: 0.8;
  }

  .file-item-container {
    position: relative;
    overflow: visible;
  }

  .file-item {
    position: relative;
    z-index: 1;

    &:active {
      background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
      transition: background-color 0.1s ease;
    }
  }

  .hidden {
    visibility: hidden;
  }

  /* 模糊遮罩 */
  .blur-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    z-index: 15;
    pointer-events: auto;
    touch-action: none; /* 禁止所有触摸操作 */
    user-select: none; /* 禁止选择 */
  }

  /* 固定克隆项容器 */
  .fixed-clone-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 25;
    pointer-events: none;
  }

  /* 克隆项样式 */
  .item-clone-wrapper {
    position: absolute;
    pointer-events: none; /* 阻止触摸事件 */
  }

  .file-item-clone {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    transform: scale(1.02);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
    pointer-events: none; /* 确保克隆项不响应触摸事件 */
  }

  /* 上下文菜单 */
  .context-menu {
    position: fixed;
    z-index: 30;
    background: rgba(249, 249, 249, 0.94);
    border-radius: 14rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
    padding: 10rpx 0;
    transform: translate(-50%, 0);
    min-width: 240rpx;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);

    &.menu-top {
      animation: fadeInTop 0.2s ease;
      transform: translate(-50%, 0);
    }

    &.menu-bottom {
      animation: fadeInBottom 0.2s ease;
      transform: translate(-50%, 0);
    }

    /* 菜单位于上方但紧贴克隆项顶部 */
    &.menu-position-bottom {
      transform: translate(-50%, 0);
    }
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 24rpx 30rpx;

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .menu-text {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #007aff;
    font-weight: 400;
  }

  .menu-delete {
    opacity: 0.9;
  }

  .menu-text-delete {
    color: #ff3b30;
  }

  .menu-divider {
    height: 1rpx;
    background-color: rgba(0, 0, 0, 0.1);
    margin: 0 10rpx;
  }

  @keyframes fadeInTop {
    from {
      opacity: 0;
      transform: translate(-50%, 10px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  @keyframes fadeInBottom {
    from {
      opacity: 0;
      transform: translate(-50%, -10px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  /* 菜单位于顶部时的自定义动画 */
  .menu-top.menu-position-bottom {
    animation: fadeInTopPosition 0.2s ease;
  }

  @keyframes fadeInTopPosition {
    from {
      opacity: 0;
      transform: translate(-50%, 10px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  .path-text {
    white-space: nowrap;
    word-break: keep-all;
  }

  /* 导航栏下拉菜单 */
  .nav-dropdown-menu {
    position: absolute;
    top: 60rpx;
    right: 0;
    z-index: 40;
    background: rgba(249, 249, 249, 0.94);
    border-radius: 14rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
    padding: 10rpx 0;
    min-width: 280rpx;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    animation: fadeInDown 0.2s ease;
    transform-origin: top right;
  }

  /* 修改下拉菜单动画，只在垂直方向有效 */
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-10rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
