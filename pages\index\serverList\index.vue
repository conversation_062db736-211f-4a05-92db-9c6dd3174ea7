<template>
	<page-container ref="pageContainer" :is-show-nav="false" :has-tab-bar="true">
		<!-- 模糊遮罩 -->
		<view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

		<!-- 悬浮上下文菜单 -->
		<view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
			<view class="menu-item menu-delete" @click="deleteModel = true">
				<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
				<text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
			</view>
		</view>

		<!-- 添加一个隐藏的临时菜单用于测量高度 -->
		<view
			class="temp-measure-menu context-menu"
			v-if="showTempMenu"
			style="position: absolute; opacity: 0; pointer-events: none; top: -9999px"
		>
			<view class="menu-item menu-delete">
				<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
				<text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
			</view>
		</view>

		<!-- 更多菜单下拉框蒙层 -->
		<view
			class="fixed top-0 left-0 right-0 bottom-0 z-39"
			v-if="showMoreMenu"
			@tap="hideMoreMenu"
			@touchmove.prevent
		></view>

		<!-- 更多菜单下拉框 -->
		<view class="more-menu" v-if="showMoreMenu" :style="moreMenuPosition" @tap.stop>
			<view class="more-menu-item" @tap="handleScanClick(startScan)">
				<uni-icons type="scan" size="16" color="var(--text-color-secondary)"></uni-icons>
				<text class="more-menu-text">扫一扫</text>
			</view>
			<view class="menu-divider"></view>
			<view class="more-menu-item" @tap="handleSortClick" v-if="configList.length > 0">
				<uni-icons type="bars" size="16" color="var(--text-color-secondary)"></uni-icons>
				<text class="more-menu-text">{{ $t('server.sort') }}</text>
			</view>
			<view class="menu-divider" v-if="configList.length > 0"></view>
			<view class="more-menu-item" @tap="handleHideIPClick">
				<uni-icons
					:type="isIPHidden ? 'eye' : 'eye-slash'"
					size="16"
					color="var(--text-color-secondary)"
				></uni-icons>
				<text class="more-menu-text">{{ isIPHidden ? '显示IP' : '隐藏IP' }}</text>
			</view>
		</view>

		<!-- 克隆项容器 - 放在外层，使用fixed定位 -->
		<view class="fixed-clone-container" v-if="showContextMenu">
			<view class="item-clone-wrapper" :style="clonePosition" v-if="activeServer">
				<view class="server-item server-item-clone" style="margin-top: 0">
					<view class="server-list-container">
						<view class="server-header">
							<view class="server-name">
								<view
									class="server-icon"
									:style="{ backgroundColor: activeServer.status ? '#20a50a' : '#f44336' }"
								></view>
								<text>{{ ` ${activeServer.name} - ${formatIPDisplay(activeServer.ip)}` }}</text>
							</view>
							<view class="server-status">
								<uv-icon name="clock" size="16" style="margin-right: 6rpx"></uv-icon>
								<text class="server-uptime">{{ activeServer.uptime }}</text>
							</view>
						</view>
						<view class="parting-line"></view>
						<view class="server-metrics">
							<!-- 负载指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.load') }}</text>
								<ECharts
									:canvas-id="`clone-load-chart`"
									chart-type="gauge"
									:chart-data="getCpuChartData(activeServer.load)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.load.total }}</text>
								</view>
							</view>

							<!-- CPU 指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.cpu') }}</text>
								<ECharts
									:canvas-id="`clone-cpu-chart`"
									chart-type="gauge"
									:chart-data="getCpuChartData(activeServer.cpu)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.cpu.cores }} {{ $t('linux.cores') }}</text>
								</view>
							</view>

							<!-- 内存指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.memory') }}</text>
								<ECharts
									:canvas-id="`clone-mem-chart`"
									chart-type="gauge"
									:chart-data="getMemChartData(activeServer.memory)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.memory.total }}</text>
								</view>
							</view>

							<!-- 磁盘指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.disk') }}(/)</text>
								<ECharts
									:canvas-id="`clone-disk-chart`"
									chart-type="gauge"
									:chart-data="getDiskChartData(activeServer.disk)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.disk.total }}</text>
								</view>
							</view>

							<!-- 网络指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.network') }}</text>
								<view class="network-stats">
									<view class="stats-row">
										<text class="stats-icon">↑</text>
										<text class="stats-value">{{ activeServer.network.up }}KB</text>
									</view>
									<view class="stats-row">
										<text class="stats-icon">↓</text>
										<text class="stats-value">{{ activeServer.network.down }}KB</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="server-list-container">
			<view v-show="configList.length > 0">
				<z-paging
					ref="paging"
					:default-page-size="10"
					use-virtual-list
					:force-close-inner-list="true"
					:auto-hide-loading-after-first-loaded="false"
					:auto-show-system-loading="true"
					:auto="false"
					:loading-more-enabled="false"
					@query="queryRules"
					@virtualListChange="virtualListChange"
					@refresherStatusChange="onRefresh"
					:refresher-complete-delay="200"
					hide-empty-view
					empty-view-text=""
				>
					<template #top>
						<custom-nav
							:title="
								configList.length > 0
									? `${$t('server.servers')} (${configList.length})`
									: $t('server.serverList')
							"
							:is-back="false"
							bg-color=""
						>
							<template #right>
								<view class="nav-right-btn">
									<uni-icons
										type="more-filled"
										class="more-server-icon"
										size="20"
										@click="toggleMoreMenu"
									></uni-icons>
								</view>
							</template>
						</custom-nav>
					</template>
					<view class="px-20">
						<uni-swipe-action>
							<uni-swipe-action-item
								class="server-item"
								v-for="(server, index) in serverList"
								:id="`zp-id-${server.zp_index}`"
								:key="server.uniqueKey || `server_${server.ip}_${server.rawPath || ''}`"
								@touchstart="handleTouchStart($event)"
								@touchmove="handleTouchMove($event)"
								@touchend="handleTouchEnd($event)"
								@touchcancel="handleTouchCancel($event)"
								:data-index="index"
								:data-server="JSON.stringify(server)"
								:right-options="options"
								@click="onActionClick($event, server)"
							>
								<view class="server-list-container" @click.stop="navigateServer(server)">
									<view class="server-header">
										<view class="server-name">
											<view
												class="server-icon"
												:style="{ backgroundColor: server.status ? '#20a50a' : '#f44336' }"
											>
											</view>
											<text>{{ ` ${server.name} - ${formatIPDisplay(server.ip)}` }}</text>
										</view>
										<view class="server-status">
											<uv-icon name="clock" size="16" style="margin-right: 6rpx"></uv-icon>
											<text class="server-uptime">{{ server.uptime }}</text>
										</view>
									</view>
									<view class="parting-line"></view>
									<view class="server-metrics">
										<!-- 负载指标 -->
										<view class="metric-item">
											<text class="metric-title">{{ $t('server.load') }}</text>
											<ECharts
												:canvas-id="`load-chart-${server.ip.replace(/\./g, '-')}`"
												chart-type="gauge"
												:chart-data="getCpuChartData(server.load)"
												:height="110"
											/>
											<view class="metric-detail-text">
												<text>{{ server.load.total }}</text>
											</view>
										</view>

										<!-- CPU 指标 -->
										<view class="metric-item">
											<text class="metric-title">{{ $t('server.cpu') }}</text>
											<ECharts
												:canvas-id="`cpu-chart-${server.ip.replace(/\./g, '-')}`"
												chart-type="gauge"
												:chart-data="getCpuChartData(server.cpu)"
												:height="110"
											/>
											<view class="metric-detail-text">
												<text>{{ server.cpu.cores }} {{ $t('linux.cores') }}</text>
											</view>
										</view>

										<!-- 内存指标 -->
										<view class="metric-item">
											<text class="metric-title">{{ $t('server.memory') }}</text>
											<ECharts
												:canvas-id="`mem-chart-${server.ip.replace(/\./g, '-')}`"
												chart-type="gauge"
												:chart-data="getMemChartData(server.memory)"
												:height="110"
											/>
											<view class="metric-detail-text">
												<text>{{ server.memory.total }}</text>
											</view>
										</view>

										<!-- 磁盘指标 -->
										<view class="metric-item">
											<text class="metric-title">{{ $t('server.disk') }}(/)</text>
											<ECharts
												:canvas-id="`disk-chart-${server.ip.replace(/\./g, '-')}`"
												chart-type="gauge"
												:chart-data="getDiskChartData(server.disk)"
												:height="110"
											/>
											<view class="metric-detail-text">
												<text>{{ server.disk.total }}</text>
											</view>
										</view>

										<!-- 网络指标 -->
										<view class="metric-item">
											<text class="metric-title">{{ $t('server.network') }}</text>
											<view class="network-stats">
												<view class="stats-row">
													<text class="stats-icon">↑</text>
													<text class="stats-value">{{ server.network.up }}KB</text>
												</view>
												<view class="stats-row">
													<text class="stats-icon">↓</text>
													<text class="stats-value">{{ server.network.down }}KB</text>
												</view>
											</view>
										</view>
									</view>
								</view>
								<view class="server-mask" v-show="!server.status">
									<view class="mask-text">{{ $t('server.serverConnectionFailed') }}</view>
								</view>
							</uni-swipe-action-item>
						</uni-swipe-action>
					</view>
				</z-paging>
			</view>
			<view v-show="configList.length === 0">
				<EmptyServerList @scan="startScan" />
			</view>
		</view>
		<CustomDialog
			v-model="deleteModel"
			contentHeight="140rpx"
			:title="$t('common.deleteConfirm')"
			@close="deleteModel = false"
			@confirm="confirmDelete"
			:confirmStyle="{
				backgroundColor: '#FF3B30',
			}"
		>
			<view class="py-20 text-secondary">
				<text>{{ $t('server.deleteConfirmText', { ip: formatIPDisplay(activeServer?.ip) }) }}</text>
			</view>
		</CustomDialog>
		<CustomDialog
			v-model="iosPromptModel"
			contentHeight="200rpx"
			title="绑定提示"
			@close="iosPromptModel = false"
			@confirm="confirmIosPrompt"
			:confirmText="$t('server.iosPrompt')"
			cancelText="重新绑定"
		>
			<view class="text-secondary flex flex-col justify-center h-full">
				<view>● 苹果手机无法信任自签证书的原因，导致扫码绑定失败</view>
				<view>● 点击查看教程里面有解决方案！</view>
			</view>
		</CustomDialog>
		<CustomDialog
			v-model="harmonyPromptModel"
			contentHeight="200rpx"
			title="绑定提示"
			@close="harmonyPromptModel = false"
			@confirm="confirmHarmonyPrompt"
			confirmText="开启并绑定"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				由于HarmonyOS系统限制，是否开启跳过网络SSL证书验证绑定？
			</view>
		</CustomDialog>
	</page-container>
	<uni-fab
		:pattern="{ buttonColor: '#20a50a' }"
		horizontal="right"
		vertical="bottom"
		v-show="serverList.length > 0"
		@click="startScan"
		:popMenu="false"
	>
	</uni-fab>
</template>

<script lang="uts" setup>
	import { ref, onMounted, computed, onUnmounted, watch } from 'vue';
	import { onShow, onHide, onLoad, onUnload, onBackPress, onReady } from '@dcloudio/uni-app';
	import { $t } from '@/locale/index.js';
	import ECharts from '@/components/ECharts/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import {
		getCpuChartData,
		getMemChartData,
		getDiskChartData,
		getDevicesInfoEvent,
		handleCloudBindSuccess,
		handleBindSuccess,
		useServerListPolling,
		getServerList,
		deleteModel,
		confirmDelete,
		getServerInfoList,
		paging,
		// 长按菜单相关
		showContextMenu,
		menuPosition,
		handleTouchStart,
		handleTouchMove,
		handleTouchEnd,
		handleTouchCancel,
		clonePosition,
		activeServer,
		hideContextMenu,
		showTempMenu,
		measureMenuHeight,
		confirmDeleteServer,
		navigateServer,
		pageContainer,
		onActionClick,
		iosPromptModel,
		confirmIosPrompt,
		harmonyPromptModel,
		harmonyInfo,
		harmonyDeviceInfo,
		// 更多菜单相关
		showMoreMenu,
		moreMenuPosition,
		toggleMoreMenu,
		hideMoreMenu,
		handleScanClick,
		handleSortClick,
		handleHideIPClick,
		// IP隐藏相关
		isIPHidden,
		initIPHiddenState,
		formatIPDisplay,
		// 手动添加服务器标志
		getIsManuallyAddingServer
	} from './useController';
	import EmptyServerList from './emptyServerList.vue';
	import useScan from '@/hooks/useScan';
	import { useServerListStore } from './store';
	import { useConfigStore } from '@/store';
	import uvNotify from '@/uni_modules/uv-notify/components/uv-notify/uv-notify.vue';
	import Message from '@/hooks/useMessage';
	// #ifdef APP-HARMONY
	import { useHttp } from '@/uni_modules/czh-http';
	// #endif

	const { serverList, serverInit } = useServerListStore().getReactiveState();
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	// 获取轮询方法
	const { startPolling, stopPolling } = useServerListPolling();

	const options = [
		{
			text: $t('common.delete'),
			style: {
				backgroundColor: '#dd2f00',
			},
		},
	];

	const notify = ref(null);

	// 添加上次刷新时间变量
	const lastRefreshTime = ref(0);

	const isRefresher = computed(() => {
		return serverList.value.length > 0;
	});

	const { isScanning, startScan } = useScan({
		onScanError(error) {
			pageContainer.value.notify.error(error.message || $t('scan.scanFail'));
		},
		onScanSuccess(res) {
			hideContextMenu();
		},
		onBindSuccess(bindInfo) {
			if (bindInfo.type === 'cloud') {
				// 处理云控绑定成功
				const { cloudCode } = bindInfo;
				handleCloudBindSuccess(cloudCode);
			} else {
				// 处理普通绑定成功
				handleBindSuccess(bindInfo);
				pageContainer.value.notify.success($t('scan.bindSuccess'));
			}
		},
		onBindError(error, bindInfo, deviceInfo) {
			pageContainer.value.notify.error(JSON.stringify(error) || $t('scan.bindFail'));
	    	// #ifdef APP-HARMONY
			if(JSON.stringify(error).includes('SSH remote key')) {
				harmonyPromptModel.value = true;
				harmonyInfo.value = bindInfo
				harmonyDeviceInfo.value = deviceInfo
				return;
			}
	    	// #endif
			if(JSON.stringify(error).includes('证书无效')) {
				console.log('证书无效');
				iosPromptModel.value = true;
				return;
			}
		},
		onLoginSuccess(loginInfo) {
			pageContainer.value.notify.success('登录成功');
		},
		onLoginError(error) {
			pageContainer.value.notify.error(error.message || $t('scan.loginFail'));
		},
		getDeviceInfo: () => {
			return {
				brand: phoneBrand.value,
				model: phoneModel.value,
			};
		},
	});

	const confirmHarmonyPrompt = () => {
		harmonyPromptModel.value = false;
		harmonySslVerification.value = true;
		uni.setStorageSync('harmonySslVerification', harmonySslVerification.value);
		useHttp(`${harmonyInfo.value.panelPath}/check_bind`, {
	      bind_token: harmonyInfo.value.uuid,
	      client_brand: harmonyDeviceInfo.value.brand,
	      client_model: harmonyDeviceInfo.value.model,
	    }).then((res) => {
			if (res.data === "0") {
				pageContainer.value.notify.error($t('server.abnormalStatus'));
				return;
			}
	        if (res.data !== "1") {
	          const decodedMessage = decodeUnicodeString(res.data);
	          pageContainer.value.notify.error(decodedMessage || $t('scan.bindFail'));
			  return;
	        }
			waitPCConfirm(harmonyInfo.value).then(() => {
	             	// 将新服务器添加到配置列表的首部（头部位置）
	             	configList.value.unshift(harmonyInfo.value);
	             	uni.setStorageSync('configList', configList.value);
					if (harmonyInfo.value.type === 'cloud') {
					// 处理云控绑定成功
					const { cloudCode } = harmonyInfo.value;
					handleCloudBindSuccess(cloudCode);
				} else {
					// 处理普通绑定成功
					handleBindSuccess(harmonyInfo.value);
				}
	           })
	           .catch((error) => {
	             pageContainer.value.notify.error(error || $t('scan.bindFail'));
	           });
	    }).catch((err) => {
	      pageContainer.value.notify.error(err || $t('scan.bindFail'));
	    });
	}

	let pcConfirmTimer = null;
	  // 等待PC端确认
	 const waitPCConfirm = (bindInfo) => {
	   return new Promise((resolve, reject) => {
	     Message.loading($t('server.waitingForPC'));
	     // 清除可能存在的定时器
	     if (pcConfirmTimer) {
	       clearInterval(pcConfirmTimer);
	       pcConfirmTimer = null;
	     }

	     pcConfirmTimer = setInterval(() => {
			useHttp(`${bindInfo.panelPath}/get_app_bind_status`, {
				bind_token: bindInfo.uuid,
			}).then((res) => {
				if (res.data === "1") {
	             clearInterval(pcConfirmTimer);
	             pcConfirmTimer = null;
	             Message.hideLoading();
	             resolve(res);
	           }
			}).catch((err) => {
				clearInterval(pcConfirmTimer);
	           pcConfirmTimer = null;
	           Message.hideLoading();
	           reject(err);
			});
	     }, 1000);

	     // 30秒超时
	     setTimeout(() => {
	       if (pcConfirmTimer) {
	         clearInterval(pcConfirmTimer);
	         pcConfirmTimer = null;
	       }
	       Message.hideLoading();
	       reject($t('server.pcConfirmTimeout'));
	     }, 30000);
	   });
	 };

	/**
	 * 列表数据变更
	 */
	const virtualListChange = (vList) => {
		// 避免直接赋值，确保数据的一致性
		// 只有当虚拟列表数据与当前 serverList 不同时才更新，避免干扰手动添加的数据和用户排序
		if (Array.isArray(vList) && vList.length >= 0) {
			// 检查是否有保存的排序顺序，如果有则不允许虚拟列表覆盖排序
			const savedSortOrder = uni.getStorageSync('serverSortOrder');
			if (savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
				console.log('检测到用户排序设置，跳过虚拟列表数据更新以保护排序');
				return;
			}

			// 检查数据是否真的发生了变化，避免不必要的更新
			const currentLength = serverList.value.length;
			const newLength = vList.length;

			// 如果长度不同，才进行更新（避免因为内容变化导致的频繁更新）
			// 注意：我们移除了内容比较，因为这可能会干扰我们的排序逻辑
			if (currentLength !== newLength) {
				console.log('虚拟列表长度变化，更新服务器列表');
				serverList.value = vList;
			}
		}
	};

	/**
	 * 查询规则列表 - 只在第一页时加载数据
	 */
	const queryRules = async (page, pageSize) => {
		try {
			// 如果正在手动添加服务器，跳过刷新以避免干扰
			if (getIsManuallyAddingServer()) {
				paging.value.completeByNoMore(serverList.value);
				return;
			}

			// 只在第一页时加载数据，避免重复加载
			if (page === 1) {
				// 获取服务器列表数据
				const res = await getServerList();

				// 确保返回的数据是数组格式
				const validData = Array.isArray(res) ? res : [];

				// 完成分页数据设置 - 使用 completeByNoMore 表示没有更多数据
				paging.value.completeByNoMore(validData);

				// 延迟更新虚拟列表渲染，确保数据已经正确设置
				setTimeout(() => {
					paging.value.updateVirtualListRender();
				}, 50);
			} else {
				// 非第一页直接返回空数组，表示没有更多数据
				paging.value.completeByNoMore([]);
			}
		} catch (error) {
			console.error('查询服务器列表失败:', error);
			paging.value.complete([]);
		}
	};

	/**
	 * 手动刷新服务器列表数据
	 */
	const refreshServerList = async () => {
		try {
			const res = await getServerList();
			const validData = Array.isArray(res) ? res : [];

			// 直接设置数据到 z-paging，不触发分页逻辑
			await paging.value.completeByNoMore(validData);

			// 检查是否有排序设置，如果没有才触发常规的虚拟列表更新
			const savedSortOrder = uni.getStorageSync('serverSortOrder');
			if (!savedSortOrder || !Array.isArray(savedSortOrder) || savedSortOrder.length === 0) {
				setTimeout(() => {
					paging.value.updateVirtualListRender();
				}, 50);
			}

			return validData;
		} catch (error) {
			console.error('刷新服务器列表失败:', error);
			throw error;
		}
	};

	const onRefresh = async (reloadType) => {
		if (reloadType === 'complete') {
			pageContainer.value.notify.success($t('common.refreshSuccess'));
		}
	};

	onShow(() => {
		startPolling();
		// 从排序页面返回时需要刷新列表以应用新的排序
		// 检查是否有排序更改标记
		const sortOrderChanged = uni.getStorageSync('sortOrderChanged');
		const savedSortOrder = uni.getStorageSync('serverSortOrder');

		if (sortOrderChanged && savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
			// 清除标记
			uni.removeStorageSync('sortOrderChanged');

			// 先刷新数据以应用排序
			refreshServerList().then(() => {
				// 然后触发 z-paging 的虚拟列表重新渲染
				setTimeout(() => {
					if (paging.value && paging.value.updateVirtualListRender) {
						paging.value.updateVirtualListRender();
					}
				}, 100);
			}).catch(error => {
				console.error('刷新服务器列表失败:', error);
			});
		}
	});

	onHide(() => {
		hideContextMenu();
		hideMoreMenu();
		stopPolling();
	});

	onLoad(() => {
		uni.$on('scan', () => {
			startScan();
		});
	});

	onUnload(() => {
		uni.$off('scan');
		deleteModel.value = false;
		iosPromptModel.value = false;
		harmonyPromptModel.value = false;
		clearInterval(pcConfirmTimer);
	});

	onMounted(() => {
		getDevicesInfoEvent();
		// 测量菜单高度
		measureMenuHeight();
		// 初始化IP隐藏状态
		initIPHiddenState();
		// 初始化时加载服务器列表
		refreshServerList();
	});

	// 监听菜单显示状态，防止页面滚动
	watch(showContextMenu, (val) => {
		if (val) {
			// 菜单显示时，禁用页面滚动
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0,
			});

			// 锁定列表滚动
			if (paging.value) {
				paging.value.lockScroll && paging.value.lockScroll(true);
			}
		} else {
			// 恢复列表滚动
			if (paging.value) {
				paging.value.lockScroll && paging.value.lockScroll(false);
			}
		}
	});

	// 监听更多菜单显示状态
	watch(showMoreMenu, (val) => {
		if (val) {
			// 如果长按菜单正在显示，先隐藏它
			if (showContextMenu.value) {
				hideContextMenu();
			}
		}
	});

	// 处理返回按钮，如果菜单正在显示，先关闭菜单
	onBackPress(() => {
		// 优先处理更多菜单的关闭
		if (showMoreMenu.value) {
			hideMoreMenu();
			return true;
		}
		// 然后处理长按菜单的关闭
		if (showContextMenu.value) {
			hideContextMenu();
			return true;
		}
		return false;
	});

	// Unicode字符串解码函数
	function decodeUnicodeString(str) {
	  try {
	    // 处理形如 \u5f53\u524d 的Unicode编码
	    return str.replace(/\\u([0-9a-fA-F]{4})/g, (match, group) => {
	      return String.fromCharCode(parseInt(group, 16));
	    });
	  } catch (e) {
	    return str; // 如果解码失败，返回原字符串
	  }
	}
</script>

<style scoped>
	.server-list-container {
		padding: 10rpx;
		background-color: var(--bg-color);
	}

	.server-list-title {
		font-size: 40rpx;
		font-weight: bold;
		color: var(--text-color-secondary);
		margin-bottom: 15rpx;
	}

	.server-item {
		background-color: var(--bg-color);
		border-radius: 12rpx;
		margin-top: 15rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
		position: relative;
		overflow: hidden;
		transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 3rpx 15rpx rgba(0, 0, 0, 0.08);
	}

	.server-item:active {
		background-color: rgba(245, 245, 245, 0.9);
		transform: scale(0.985);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
	}

	.server-header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8rpx;
		padding: 3rpx 0;
	}

	.server-name {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		font-weight: 500;
		color: var(--text-color-tertiary);
		max-width: 75%;
	}

	.server-name text {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.server-icon {
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		margin-right: 8rpx;
		flex-shrink: 0;
		box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
	}

	.server-ip {
		font-size: 28rpx !important;
		color: var(--text-color-tertiary);
		opacity: 0.85;
	}

	.server-status {
		display: flex;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.03);
		padding: 4rpx 8rpx;
		border-radius: 24rpx;
	}

	.server-uptime {
		color: var(--text-color-tertiary);
		font-size: 24rpx;
		margin-right: 20rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.server-version {
		color: var(--text-color-tertiary);
		font-size: 28rpx;
	}

	.server-metrics {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 3rpx 0;
	}

	.metric-item {
		width: 18%;
		text-align: center;
		margin-bottom: 4rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		transition: transform 0.2s ease;
	}

	.metric-item:hover {
		transform: translateY(-2rpx);
	}

	.echarts-view {
		width: 100% !important;
		height: 50px !important;
		display: block;
		position: relative;
	}

	.echarts-container {
		width: 100%;
		height: 50px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: transparent !important;
		padding: 0 !important;
		box-shadow: none !important;
	}

	.metric-title {
		color: var(--text-color-tertiary);
		font-size: 20rpx;
		margin-bottom: 2rpx;
		font-weight: 400;
		letter-spacing: 0.2rpx;
	}

	.metric-detail-text {
		display: flex;
		flex-direction: column;
		position: relative;
		text-align: center;
		color: var(--text-color-tertiary);
		font-size: 20rpx;
		opacity: 0.9;
	}

	.network-stats,
	.io-stats {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 2rpx 0;
		height: 50px;
		justify-content: space-around;
		background-color: rgba(0, 0, 0, 0.02);
		border-radius: 6rpx;
		width: 75%;
	}

	.stats-row {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 1rpx 0;
	}

	.stats-icon {
		color: var(--text-color-tertiary);
		font-size: 20rpx;
		margin-right: 4rpx;
		font-weight: normal;
	}

	.stats-value {
		color: var(--text-color-tertiary);
		font-size: 20rpx;
		font-weight: 400;
	}

	.parting-line {
		width: 100%;
		height: 1rpx;
		background: linear-gradient(to right, transparent, var(--text-color-tertiary), transparent);
		margin: 8rpx 0;
		opacity: 0.3;
	}

	.add-server-icon {
		color: var(--text-color-secondary) !important;
		border: 4rpx solid var(--text-color-secondary) !important;
		border-radius: 50%;
		margin-right: 20rpx;
		transition: all 0.2s ease;
	}

	.add-server-icon:active {
		opacity: 0.7;
		transform: scale(0.95);
	}

	.more-server-icon {
		color: var(--text-color-secondary) !important;
		margin-right: 20rpx;
		transition: all 0.2s ease;
	}

	.more-server-icon:active {
		opacity: 0.7;
		transform: scale(0.95);
	}

	.server-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(var(--bg-color-secondary-rgb), 0.4);
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 20rpx;
	}

	.mask-text {
		font-size: 28rpx;
		color: var(--text-color-secondary);
		text-align: center;
		padding: 20rpx 32rpx;
		background: var(--bg-color);
		border-radius: 16rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	}

	/* 长按菜单相关样式 */
	/* 模糊遮罩 */
	.blur-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.4);
		backdrop-filter: blur(6px);
		-webkit-backdrop-filter: blur(6px);
		z-index: 15;
		pointer-events: auto;
		touch-action: none;
		/* 禁止所有触摸操作 */
		user-select: none;
		/* 禁止选择 */
	}

	/* 上下文菜单 */
	.context-menu {
		position: fixed;
		z-index: 30;
		background: rgba(249, 249, 249, 0.96);
		border-radius: 18rpx;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
		padding: 10rpx 0;
		transform: translate(-50%, 0);
		min-width: 260rpx;
		backdrop-filter: blur(25px);
		-webkit-backdrop-filter: blur(25px);
		border: 0.5px solid rgba(0, 0, 0, 0.05);
	}

	.menu-top {
		animation: fadeInTop 0.25s cubic-bezier(0.4, 0, 0.2, 1);
		transform: translate(-50%, 0);
	}

	.menu-bottom {
		animation: fadeInBottom 0.25s cubic-bezier(0.4, 0, 0.2, 1);
		transform: translate(-50%, 0);
	}

	/* 菜单位于上方但紧贴克隆项顶部 */
	.menu-position-bottom {
		transform: translate(-50%, 0);
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 16rpx 24rpx;
		transition: background-color 0.15s ease;
	}

	.menu-item:active {
		background-color: rgba(0, 0, 0, 0.06);
	}

	.menu-text {
		margin-left: 10rpx;
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}

	.menu-delete {
		opacity: 0.95;
	}

	.menu-text-delete {
		color: #ff3b30;
	}

	/* 固定克隆项容器 */
	.fixed-clone-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 25;
		pointer-events: none;
	}

	/* 克隆项样式 */
	.item-clone-wrapper {
		position: absolute;
		pointer-events: none;
		/* 阻止触摸事件 */
		width: 100%;
		padding: 0 20rpx;
		box-sizing: border-box;
	}

	.server-item-clone {
		opacity: 0.98;
		transform: scale(1.05);
		box-shadow: 0 12rpx 36rpx rgba(0, 0, 0, 0.15);
		transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
	}

	@keyframes fadeInTop {
		from {
			opacity: 0;
			transform: translate(-50%, 12rpx);
		}

		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	@keyframes fadeInBottom {
		from {
			opacity: 0;
			transform: translate(-50%, -12rpx);
		}

		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	/* 更多菜单样式 */
	.more-menu {
		position: fixed;
		z-index: 40;
		background: var(--dialog-bg-color);
		border-radius: 14rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
		padding: 10rpx 0;
		min-width: 240rpx;
		backdrop-filter: blur(20px);
		-webkit-backdrop-filter: blur(20px);
		animation: fadeInDown 0.2s ease;
		transform-origin: top right;
	}

	.more-menu-item {
		display: flex;
		align-items: center;
		padding: 24rpx 30rpx;
		transition: background-color 0.15s ease;
	}

	.more-menu-item:active {
		background-color: rgba(0, 0, 0, 0.05);
	}

	.more-menu-text {
		margin-left: 20rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: var(--text-color-secondary);
	}

	.menu-divider {
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		margin: 0 10rpx;
	}

	/* 修改下拉菜单动画，只在垂直方向有效 */
	@keyframes fadeInDown {
		from {
			opacity: 0;
			transform: translateY(-10rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
