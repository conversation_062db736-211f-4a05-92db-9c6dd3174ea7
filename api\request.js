import {
	PATH, // 面板路径
	UUID, // 用户唯一标识
	SECRET, // api秘钥
	AESKEY, // aes
	SYSTEM, // 系统
	encrypt, // aes加密
	decrypt, // aes解密
} from '@/utils/config';
import Message from '@/hooks/useMessage';
import md5 from 'js-md5';
import { useConfigStore } from '@/store';
import AdapterFactory from './adapters/AdapterFactory';
import { isFromNodeAccess } from '@/utils/nodeConfigManager';
// #ifdef APP-HARMONY
import { useHttp } from '@/uni_modules/czh-http';
// #endif

// 获取环境变量中设置的面板所有者类型
const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

// 将 store 的使用移到函数内部
const getConfigList = () => {
	const store = useConfigStore();
	return store.getReactiveState().configList;
};

const getHarmonySslVerification = () => {
	const store = useConfigStore();
	return store.getReactiveState().harmonySslVerification.value;
};

/**
 * 处理请求响应
 * @param {Object} res 响应对象
 * @param {string} tempAesKey AES密钥
 * @param {string} tempUUID 用户ID
 * @param {string} tempPath 路径
 * @param {boolean} is_verify 是否验证
 * @param {Function} customResponseHandler 自定义响应处理函数
 * @param {string} apiType API类型
 * @returns {Promise} 处理后的响应
 */
const handleResponse = (
	res,
	tempAesKey,
	tempUUID,
	tempPath,
	is_verify,
	customResponseHandler,
	apiType,
	resolve,
	reject,
	skipDecrypt,
) => {
	// 根据API类型判断响应状态
	const isErrorResponse =
		apiType === 'international'
			? is_verify && res.data && res.data.status !== 0 && res.data.status != undefined
			: is_verify &&
			  res.data &&
			  res.data.status != undefined &&
			  !res.data.status &&
			  res.data.msg != '离线模式下无法使用此功能!';

	if (isErrorResponse) {
		let deleteUUID = tempUUID;
		let routes = getCurrentPages(),
			list = getConfigList().value,
			currentPage = routes[routes.length - 1].route;
		// for (let i = 0; i < list.length; i++) {
		//   if (list[i].uuid === deleteUUID) {
		//     list.splice(i, 1);
		//     updateConfigList(list);
		//     break;
		//   }
		// }

		let showPath = tempPath.substring(tempPath.lastIndexOf('/') + 1, tempPath.lastIndexOf(':'));
		Message.info('检测到[' + showPath + ']在PC端的授权已删除，已从App上解绑该服务器！', 4000);

		// if (currentPage !== 'pages/index/index') {
		//   uni.reLaunch({
		//     url: '/pages/index/index',
		//   });
		// }
		resolve(res);
	} else {
		try {
			// 获取适配器
			const adapter = AdapterFactory.getAdapter(apiType);
			let resData;
			if (typeof res.data === 'string' && !skipDecrypt) {
				resData = JSON.parse(decrypt(res.data, tempAesKey));
			} else {
				resData = res.data;
			}
			// 1. 首先经过适配器处理
			resData = adapter.transformResponse(resData);
			// 2. 如果提供了自定义响应处理函数，则进一步处理
			if (typeof customResponseHandler === 'function') {
				resData = customResponseHandler(resData);
			}
			resolve(resData);
		} catch (e) {
			console.log(e);
			if (res.statusCode == 200) {
				resolve(res);
			} else {
				resolve({
					status: false,
					data: res.data,
				});
			}
		}
	}
};

/**
 * 处理请求错误
 * @param {Object} err 错误对象
 * @param {Function} customResponseHandler 自定义响应处理函数
 * @param {string} apiType API类型
 * @param {Function} reject Promise的reject函数
 */
const handleRequestError = (err, customResponseHandler, apiType, reject) => {
	uni.hideLoading();
	uni.stopPullDownRefresh();
	// 获取适配器
	const adapter = AdapterFactory.getAdapter(apiType);
	// 使用适配器处理错误
	const handledError = adapter.handleError(err);

	// 如果提供了自定义响应处理函数，也可以处理错误
	if (typeof customResponseHandler === 'function') {
		try {
			const customHandledError = customResponseHandler({
				status: false,
				error: handledError,
				isError: true,
			});
			reject(customHandledError);
		} catch (e) {
			reject(handledError);
		}
	} else {
		reject(handledError);
	}
};

const isHarmonyOS = () => {
	const systemInfo = uni.getSystemInfoSync();
	return systemInfo.system && systemInfo.system.toLowerCase().includes('harmony');
};

/**
 * 请求函数
 * @param {string} url 请求路径
 * @param {Object} data 请求数据
 * @param {string} type 请求类型
 * @param {Function} customResponseHandler 自定义响应处理函数
 * @param {Object} config 请求配置
 * @param {string} apiType API类型
 * @returns {Promise} Promise对象
 */
const axios = (
	url,
	data = {},
	type = 'POST',
	customResponseHandler = null,
	config = {},
	apiType = DEFAULT_API_TYPE,
) => {
	// 获取适配器
	const adapter = AdapterFactory.getAdapter(apiType);

	let timestamp = Date.parse(new Date()),
		request_token = md5(String(timestamp) + md5(SECRET.value));
	let paramJson = {};
	let is_verify = true;
	// 跳过解密
	let skipDecrypt = false;

	data['request_time'] = timestamp;
	data['request_token'] = request_token;

	// 根据是否为节点访问来决定是否添加 client_bind_token
	if (!isFromNodeAccess()) {
		// 正常访问时添加 client_bind_token
		paramJson['client_bind_token'] = UUID.value;
	}

	paramJson['form_data'] = encrypt(JSON.stringify(data), AESKEY.value);
	if (isFromNodeAccess()) {
		paramJson['request_time'] = timestamp;
		paramJson['request_token'] = request_token;
	}

	is_verify = data['is_verify'] ?? true;
	skipDecrypt = data['skipDecrypt'] ?? false;

	let header = {
		'Content-Type': 'application/x-www-form-urlencoded',
	};

	// 在接口数据返回来后，这些值会被覆盖，在这设置个临时变量
	let tempAesKey = AESKEY.value,
		tempUUID = UUID.value,
		tempPath = PATH.value;

	return new Promise(function (resolve, reject) {
		// 根据SSL验证配置选择请求方式
		const useSslVerification = getHarmonySslVerification();

		if (useSslVerification && isHarmonyOS()) {
			// 使用useHttp处理请求
			useHttp(PATH.value + url, paramJson, type)
				.then((res) => {
					handleResponse(
						res,
						tempAesKey,
						tempUUID,
						tempPath,
						is_verify,
						customResponseHandler,
						apiType,
						resolve,
						reject,
					);
				})
				.catch((err) => {
					handleRequestError(err, customResponseHandler, apiType, reject);
				});
		} else {
			// 使用uni.request处理请求
			uni.request({
				url: PATH.value + url,
				data: data,
				method: type,
				header: Object.assign({}, header, config),
				sslVerify: false,
				timeout: 60000,
				success: function (res) {
					handleResponse(
						res,
						tempAesKey,
						tempUUID,
						tempPath,
						is_verify,
						customResponseHandler,
						apiType,
						resolve,
						reject,
						skipDecrypt,
					);
				},
				fail: function (err) {
					handleRequestError(err, customResponseHandler, apiType, reject);
				},
			});
		}
	});
};

export default axios;
