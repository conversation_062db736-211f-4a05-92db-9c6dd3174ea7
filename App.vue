<script>
	import { setTheme, getFollowSystem } from './hooks/useTheme.js';
	import { stopRequestPermissionListener } from '@/uni_modules/x-perm-apply-instr-v2/js_sdk/index.js';

	export default {
		onLaunch: function () {
			console.log('App Launch');

			// 全局存储主题信息，使所有组件都能访问
			this.globalData = this.globalData || {};
			this.globalData.theme = theme;

			// 监听系统主题变化
			if (uni.canIUse('onThemeChange')) {
				uni.onThemeChange(({ theme }) => {
					// 如果设置了跟随系统主题，则跟随系统主题变化
					if (getFollowSystem()) {
						const newTheme = theme === 'dark' ? 'dark' : 'light';
						setTheme(newTheme);
						this.globalData.theme = newTheme;
					}
				});
			}

			/* 锁定屏幕方向，竖屏正方向,APP执行代码 */
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('portrait-primary');
			// #endif
		},
		onShow: function () {
			console.log('App Show');
		},
		onHide: function () {
			console.log('App Hide');
		},
		onExit: function () {
			console.log('App Exit');
			stopRequestPermissionListener();
		},
	};
</script>

<style>
	@import '@/static/iconfont/iconfont.css';
</style>
<style lang="scss">
	/* 每个页面公共css */
	@import '@/uni.scss';
	@import '@/styles/theme.scss';
	@import './js_sdk/a-hua-unocss/index.scss';
</style>
